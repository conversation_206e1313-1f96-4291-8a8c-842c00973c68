module.exports = {
  // HTTP Status Codes
  HTTP_STATUS: {
    OK: 200,
    CREATED: 201,
    NO_CONTENT: 204,
    BAD_REQUEST: 400,
    UNAUTHORIZED: 401,
    FORBIDDEN: 403,
    NOT_FOUND: 404,
    CONFLICT: 409,
    UNPROCESSABLE_ENTITY: 422,
    TOO_MANY_REQUESTS: 429,
    INTERNAL_SERVER_ERROR: 500,
    SERVICE_UNAVAILABLE: 503
  },

  // User Roles
  USER_ROLES: {
    USER: 'user',
    ADMIN: 'admin',
    MODERATOR: 'moderator',
    GUEST: 'guest'
  },

  // User Status
  USER_STATUS: {
    ACTIVE: 'active',
    INACTIVE: 'inactive',
    SUSPENDED: 'suspended',
    PENDING: 'pending'
  },

  // Order Status
  ORDER_STATUS: {
    PENDING: 'pending',
    CONFIRMED: 'confirmed',
    PROCESSING: 'processing',
    SHIPPED: 'shipped',
    DELIVERED: 'delivered',
    CANCELLED: 'cancelled',
    REFUNDED: 'refunded',
    RETURNED: 'returned'
  },

  // Payment Status
  PAYMENT_STATUS: {
    PENDING: 'pending',
    PAID: 'paid',
    FAILED: 'failed',
    REFUNDED: 'refunded',
    PARTIALLY_REFUNDED: 'partially_refunded'
  },

  // Payment Methods
  PAYMENT_METHODS: {
    CREDIT_CARD: 'credit_card',
    DEBIT_CARD: 'debit_card',
    PAYPAL: 'paypal',
    STRIPE: 'stripe',
    CASH: 'cash',
    BANK_TRANSFER: 'bank_transfer'
  },

  // Event Types
  EVENT_TYPES: {
    USER: {
      CREATED: 'user.created',
      UPDATED: 'user.updated',
      DELETED: 'user.deleted',
      LOGIN: 'user.login',
      LOGOUT: 'user.logout',
      PASSWORD_CHANGED: 'user.password-changed',
      EMAIL_VERIFIED: 'user.email-verified'
    },
    ORDER: {
      CREATED: 'order.created',
      UPDATED: 'order.updated',
      CANCELLED: 'order.cancelled',
      SHIPPED: 'order.shipped',
      DELIVERED: 'order.delivered',
      PAYMENT_RECEIVED: 'order.payment-received',
      REFUNDED: 'order.refunded'
    }
  },

  // Queue Names
  QUEUES: {
    USER_EVENTS: 'user.events',
    ORDER_EVENTS: 'order.events',
    NOTIFICATION_EVENTS: 'notification.events',
    EMAIL_QUEUE: 'email.queue',
    SMS_QUEUE: 'sms.queue'
  },

  // Cache Keys
  CACHE_KEYS: {
    USER: 'user',
    ORDER: 'order',
    PRODUCT: 'product',
    SESSION: 'session',
    RATE_LIMIT: 'rate'
  },

  // Time Constants
  TIME: {
    ONE_MINUTE: 60,
    FIVE_MINUTES: 300,
    ONE_HOUR: 3600,
    ONE_DAY: 86400,
    ONE_WEEK: 604800,
    ONE_MONTH: 2592000
  },

  // Pagination
  PAGINATION: {
    DEFAULT_PAGE: 1,
    DEFAULT_LIMIT: 20,
    MAX_LIMIT: 100
  },

  // File Upload
  FILE_UPLOAD: {
    MAX_SIZE: 10 * 1024 * 1024, // 10MB
    ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'],
    ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.pdf']
  },

  // Regular Expressions
  REGEX: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE: /^\+?[1-9]\d{1,14}$/,
    UUID: /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i,
    ALPHANUMERIC: /^[a-zA-Z0-9]+$/,
    URL: /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/
  },

  // Error Messages
  ERROR_MESSAGES: {
    UNAUTHORIZED: 'Unauthorized access',
    FORBIDDEN: 'Access forbidden',
    NOT_FOUND: 'Resource not found',
    VALIDATION_FAILED: 'Validation failed',
    INTERNAL_ERROR: 'Internal server error',
    SERVICE_UNAVAILABLE: 'Service temporarily unavailable',
    RATE_LIMIT_EXCEEDED: 'Rate limit exceeded',
    INVALID_TOKEN: 'Invalid or expired token',
    DUPLICATE_ENTRY: 'Duplicate entry found'
  },

  // Success Messages
  SUCCESS_MESSAGES: {
    CREATED: 'Resource created successfully',
    UPDATED: 'Resource updated successfully',
    DELETED: 'Resource deleted successfully',
    OPERATION_SUCCESSFUL: 'Operation completed successfully'
  },

  // Environment
  ENVIRONMENTS: {
    DEVELOPMENT: 'development',
    STAGING: 'staging',
    PRODUCTION: 'production',
    TEST: 'test'
  },

  // Security
  SECURITY: {
    JWT_EXPIRES_IN: '7d',
    REFRESH_TOKEN_EXPIRES_IN: '30d',
    PASSWORD_RESET_EXPIRES_IN: '1h',
    EMAIL_VERIFICATION_EXPIRES_IN: '24h',
    MAX_LOGIN_ATTEMPTS: 5,
    LOCK_TIME: 2 * 60 * 60 * 1000, // 2 hours
    BCRYPT_ROUNDS: 10
  }
};