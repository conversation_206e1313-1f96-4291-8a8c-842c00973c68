const { User } = require('../models');
const { createBaseRepository } = require('./index');
const { cache } = require('../config/cache.config');
const { logger } = require('../utils/logger');

const createUserRepository = ({ models }) => {
  const User = models.User
  const baseRepository = createBaseRepository(User);

  return {
    ...baseRepository,

    async findByEmail(email) {
      const cacheKey = `user:email:${email}`;

      // Try cache first
      const cached = await cache.get(cacheKey);
      if (cached) {
        const object = User.build(cached, { isNewRecord: false })
        logger.debug(`Cache hit for user email: ${email}`);
        return object;
      }

      const user = await User.findByEmail(email);

      if (user) {
        await cache.set(cacheKey, user.toJSON(), 3600);
      }

      return user;
    },

    async findByUsername(username) {
      const cacheKey = `user:username:${username}`;

      // Try cache first
      const cached = await cache.get(cacheKey);
      if (cached) {
        logger.debug(`Cache hit for username: ${username}`);
        return cached;
      }

      const user = await User.findByUsername(username);

      if (user) {
        await cache.set(cacheKey, user.toJSON(), 3600);
      }

      return user;
    },

    async findActiveUsers(options = {}) {
      return baseRepository.findAll({ status: 'active' }, options);
    },

    async searchUsers(searchTerm, options = {}) {
      const { Op } = require('sequelize');

      return baseRepository.findAll({
        [Op.or]: [
          { email: { [Op.like]: `%${searchTerm}%` } },
          { username: { [Op.like]: `%${searchTerm}%` } },
          { firstName: { [Op.like]: `%${searchTerm}%` } },
          { lastName: { [Op.like]: `%${searchTerm}%` } }
        ]
      }, options);
    },

    async updateLastLogin(userId) {
      return baseRepository.update(userId, {
        lastLoginAt: new Date(),
        loginAttempts: 0,
        lockedUntil: null
      });
    },

    async verifyEmail(userId, token) {
      const user = await baseRepository.findById(userId);

      if (!user || user.emailVerificationToken !== token) {
        throw new Error('Invalid verification token');
      }

      return baseRepository.update(userId, {
        emailVerified: true,
        emailVerificationToken: null
      });
    },

    async setPasswordResetToken(userId, token, expires) {
      return baseRepository.update(userId, {
        passwordResetToken: token,
        passwordResetExpires: expires
      });
    },

    async resetPassword(userId, token, newPassword) {
      const user = await baseRepository.findById(userId);

      if (!user || user.passwordResetToken !== token) {
        throw new Error('Invalid reset token');
      }

      if (user.passwordResetExpires < new Date()) {
        throw new Error('Reset token expired');
      }

      return baseRepository.update(userId, {
        password: newPassword,
        passwordResetToken: null,
        passwordResetExpires: null
      });
    },

    async getUserStatistics() {
      const { Op } = require('sequelize');
      const { masterDb } = require('../config/database.config');

      const stats = await User.findAll({
        attributes: [
          [masterDb.fn('COUNT', masterDb.col('id')), 'total'],
          [masterDb.fn('COUNT', masterDb.literal('CASE WHEN status = "active" THEN 1 END')), 'active'],
          [masterDb.fn('COUNT', masterDb.literal('CASE WHEN emailVerified = true THEN 1 END')), 'verified'],
          'role'
        ],
        group: ['role']
      });

      return stats;
    },

    async cleanupExpiredTokens() {
      const { Op } = require('sequelize');

      return baseRepository.updateWhere({
        passwordResetExpires: {
          [Op.lt]: new Date()
        }
      }, {
        passwordResetToken: null,
        passwordResetExpires: null
      });
    }
  };
};

module.exports = { createUserRepository };