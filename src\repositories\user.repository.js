const { createBaseRepository } = require('./index');
const { cache } = require('../config/cache.config');
const { logger } = require('../utils/logger');

const createUserRepository = ({ models }) => {
  const User = models.User;
  const baseRepository = createBaseRepository(User);

  return {
    ...baseRepository,

    async findByEmail(email) {
  const cacheKey = `user:email:${email}`;

  // ✅ Try cache first
  const cached = await cache.get(cacheKey);
  if (cached) {
    logger.debug(`Cache hit for user email: ${email}`);
    
    // ❗ Re-fetch full instance (to include password + methods)
    return await User.findOne({
      where: { email },
      attributes: { include: ['password'] }
    });
  }

  // 🟡 Fallback to DB
  const user = await User.findOne({
    where: { email },
    attributes: { include: ['password'] }
  });

  if (user) {
    // ✅ Remove password before caching
    const { password, ...safeUser } = user.toJSON();
    await cache.set(cacheKey, safeUser, 3600);
  }

  return user;
},

    // async findByUsername(username) {
    //   const cacheKey = `user:username:${username}`;

    //   // Try cache first
    //   const cached = await cache.get(cacheKey);
    //   if (cached) {
    //     logger.debug(`Cache hit for username: ${username}`);
    //     return cached;
    //   }

    //   const user = await User.findByUsername(username);

    //   if (user) {
    //     await cache.set(cacheKey, user.toJSON(), 3600);
    //   }

    //   return user;
    // },

    async findActiveUsers(options = {}) {
      return baseRepository.findAll({ status: 'active' }, options);
    },

    async searchUsers(searchTerm, options = {}) {
      const { Op } = require('sequelize');

      return baseRepository.findAll({
        [Op.or]: [
          { email: { [Op.like]: `%${searchTerm}%` } },
          { username: { [Op.like]: `%${searchTerm}%` } },
          { firstName: { [Op.like]: `%${searchTerm}%` } },
          { lastName: { [Op.like]: `%${searchTerm}%` } }
        ]
      }, options);
    },

    async updateLastLogin(id) {
      return baseRepository.update(id, {
        lastLoginAt: new Date(),
        loginAttempts: 0,
        lockedUntil: null
      });
    },


    async verifyEmail(id, token) {
      const user = await baseRepository.findById(id);

      if (!user || user.emailVerificationToken !== token) {
        throw new Error('Invalid verification token');
      }

      return baseRepository.update(id, {
        emailVerified: true,
        emailVerificationToken: null
      });
    },

    async setPasswordResetToken(id, token, expires) {
      return baseRepository.update(id, {
        passwordResetToken: token,
        passwordResetExpires: expires
      });
    },

    async resetPassword(id, token, newPassword) {
      const user = await baseRepository.findById(id);

      if (!user || user.passwordResetToken !== token) {
        throw new Error('Invalid reset token');
      }

      if (user.passwordResetExpires < new Date()) {
        throw new Error('Reset token expired');
      }

      return baseRepository.update(id, {
        password: newPassword,
        passwordResetToken: null,
        passwordResetExpires: null
      });
    },

    async getUserStatistics() {
      const { Op } = require('sequelize');
      const { masterDb } = require('../config/database.config');

      const stats = await User.findAll({
        attributes: [
          [masterDb.fn('COUNT', masterDb.col('id')), 'total'],
          [masterDb.fn('COUNT', masterDb.literal('CASE WHEN status = "active" THEN 1 END')), 'active'],
          [masterDb.fn('COUNT', masterDb.literal('CASE WHEN emailVerified = true THEN 1 END')), 'verified'],
          'role'
        ],
        group: ['role']
      });

      return stats;
    },

    async cleanupExpiredTokens() {
      const { Op } = require('sequelize');

      return baseRepository.updateWhere({
        passwordResetExpires: {
          [Op.lt]: new Date()
        }
      }, {
        passwordResetToken: null,
        passwordResetExpires: null
      });
    }
  };
};

module.exports = { createUserRepository };