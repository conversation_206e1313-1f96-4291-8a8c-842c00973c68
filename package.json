{"name": "enterprise-node-app", "version": "1.0.0", "description": "Enterprise-grade Node.js application with advanced architecture", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest --coverage", "test:watch": "jest --watch", "test:unit": "jest tests/unit", "test:integration": "jest tests/integration", "test:e2e": "jest tests/e2e --runInBand", "test:coverage": "jest --coverage --coverageReporters=text-lcov | coveralls", "lint": "eslint src --ext .js", "lint:fix": "eslint src --ext .js --fix", "format": "prettier --write \"src/**/*.js\" \"tests/**/*.js\"", "db:create": "sequelize-cli db:create", "db:migrate": "sequelize-cli db:migrate", "db:migrate:undo": "sequelize-cli db:migrate:undo", "db:seed": "sequelize-cli db:seed:all", "db:seed:undo": "sequelize-cli db:seed:undo:all", "generate:module": "node scripts/generate-module.js", "generate:migration": "sequelize-cli migration:generate --name", "generate:seed": "sequelize-cli seed:generate --name", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:reload": "pm2 reload ecosystem.config.js", "pm2:logs": "pm2 logs", "pm2:monit": "pm2 monit", "docker:build": "docker build -t enterprise-app .", "docker:run": "docker run -p 3000:3000 --env-file .env enterprise-app", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "clean": "rm -rf node_modules coverage logs temp uploads", "clean:install": "npm run clean && npm install", "validate": "npm run lint && npm run test", "pre-commit": "npm run lint && npm run test:unit", "release": "standard-version", "release:minor": "standard-version --release-as minor", "release:major": "standard-version --release-as major"}, "dependencies": {"@fastify/cors": "11.0.1", "@fastify/helmet": "^13.0.1", "@fastify/jwt": "9.1.0", "@fastify/rate-limit": "10.3.0", "@fastify/swagger": "9.5.1", "@fastify/swagger-ui": "^5.2.3", "@grpc/grpc-js": "^1.13.4", "@grpc/proto-loader": "^0.7.15", "@grpc/reflection": "^1.0.4", "@socket.io/redis-adapter": "^8.3.0", "amqplib": "^0.10.8", "axios": "^1.7.7", "bcrypt": "6.0.0", "dayjs": "^1.11.13", "dotenv": "^16.6.1", "fastify": "^5.3.3", "ioredis": "^5.6.1", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "mqtt": "^5.13.1", "pg": "^8.12.0", "pino": "^9.7.0", "pino-pretty": "^13.0.0", "prom-client": "^15.1.3", "redis": "^5.1.1", "sequelize": "^6.37.7", "socket.io": "^4.8.0", "uuid": "^10.0.0"}, "devDependencies": {"@faker-js/faker": "^9.0.3", "jest": "^29.7.0", "nodemon": "^3.1.4", "sequelize-cli": "^6.6.2", "supertest": "^7.0.0"}, "jest": {"testEnvironment": "node", "coverageDirectory": "coverage", "collectCoverageFrom": ["src/**/*.js", "!src/server.js"], "testMatch": ["**/tests/**/*.test.js"]}}