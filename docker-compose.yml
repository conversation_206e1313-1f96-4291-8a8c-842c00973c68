services:
  # PostgreSQL Primary
  postgres-primary:
    image: postgres:16
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: 1234
      POSTGRES_DB: app_db
      REPLICATION_USER: replicator
      REPLICATION_PASSWORD: 1234
    ports:
      - "5432:5432"
    volumes:
      - postgres-primary-data:/var/lib/postgresql/data
      - ./init-primary.sh:/docker-entrypoint-initdb.d/init-primary.sh
    command: >
      postgres
      -c wal_level=replica
      -c hot_standby=on
      -c max_wal_senders=3
      -c max_replication_slots=3
      -c listen_addresses=*
      -c hot_standby_feedback=on

  # PostgreSQL Replica
  postgres-replica:
    image: postgres:16
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: 1234
      POSTGRES_DB: app_db
      REPLICATION_USER: replicator
      REPLICATION_PASSWORD: 1234
    ports:
      - "5433:5432"
    volumes:
      - postgres-replica-data:/var/lib/postgresql/data
      - ./init-replica.sh:/docker-entrypoint-initdb.d/init-replica.sh
    depends_on:
      - postgres-primary
    command: >
      postgres
      -c hot_standby=on

  # PostgreSQL Exporter
  postgres-exporter:
    image: prometheuscommunity/postgres-exporter:latest
    environment:
      DATA_SOURCE_NAME: "********************************************/app_db?sslmode=disable"
    ports:
      - "9187:9187"
    depends_on:
      - postgres-primary

  # Redis Cluster
  redis-node-1:
    image: redis:7-alpine
    command: redis-server --port 7000 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7003:7000"

  redis-node-2:
    image: redis:7-alpine
    command: redis-server --port 7001 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7001:7001"

  redis-node-3:
    image: redis:7-alpine
    command: redis-server --port 7002 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    ports:
      - "7002:7002"

  # Redis Exporter for each node
  redis-exporter-1:
    image: oliver006/redis_exporter:latest
    environment:
      REDIS_ADDR: redis-node-1:7000
    ports:
      - "9121:9121"
    depends_on:
      - redis-node-1

  redis-exporter-2:
    image: oliver006/redis_exporter:latest
    environment:
      REDIS_ADDR: redis-node-2:7001
    ports:
      - "9122:9121"
    depends_on:
      - redis-node-2

  redis-exporter-3:
    image: oliver006/redis_exporter:latest
    environment:
      REDIS_ADDR: redis-node-3:7002
    ports:
      - "9123:9121"
    depends_on:
      - redis-node-3

  # RabbitMQ
  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin

  # Zookeeper
  zookeeper:
    image: confluentinc/cp-zookeeper:7.5.0
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2181:2181"

  # Zookeeper Exporter
  zookeeper-exporter:
    image: dabealu/zookeeper-exporter:latest
    environment:
      ZK_HOSTS: zookeeper:2181
    ports:
      - "9141:9141"
    depends_on:
      - zookeeper

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:7.5.0
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
            KAFKA_BROKER_ID: 1
            KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
            KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
            KAFKA_LISTENERS: PLAINTEXT://0.0.0.0:9092
            KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
            KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"

  # Kafka JMX Exporter
  kafka-exporter:
    image: danielqsj/kafka-exporter:latest
    command: --kafka.server=kafka:9092
    ports:
      - "9308:9308"
    depends_on:
      - kafka

  # MQTT Broker
  mosquitto:
    image: eclipse-mosquitto:latest
    ports:
      - "1883:1883"
      - "9001:9001"
    volumes:
      - ./infrastructure/mosquitto.conf:/mosquitto/config/mosquitto.conf
      - mosquitto-data:/mosquitto/data

  # MQTT Metrics Publisher
  mqtt-metrics-publisher:
    build:
      context: .
      dockerfile: Dockerfile.mqtt-publisher
    depends_on:
      - mosquitto

  # MQTT Exporter
  mqtt-exporter:
    image: sapcc/mosquitto-exporter:latest
    environment:
      MQTT_ADDRESS: mqtt://mosquitto:1883
      MQTT_TOPIC: "metrics/#"
    ports:
      - "9641:9641"
    depends_on:
      - mosquitto
      - mqtt-metrics-publisher

  # Prometheus
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./infrastructure/prometheus.yml:/etc/prometheus/prometheus.yml
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
    depends_on:
      - postgres-exporter
      - redis-exporter-1
      - redis-exporter-2
      - redis-exporter-3
      - rabbitmq
      - kafka-exporter
      - mqtt-exporter
      - zookeeper-exporter

  # Grafana
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    depends_on:
      - prometheus

  # Adminer
  adminer:
    image: adminer
    restart: always
    ports:
      - "8080:8080"

volumes:
  postgres-primary-data:
  postgres-replica-data:
  mosquitto-data: