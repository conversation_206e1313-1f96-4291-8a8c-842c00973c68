const { publishToRabbit } = require('../config/queue.config');
const { logger } = require('../utils/logger');

const createNotificationService = ({ notificationStrategy }) => {
  return {
    async sendEmail(to, subject, template, data) {
      try {
        const result = await notificationStrategy.execute('email', {
          to,
          subject,
          template,
          data
        });

        logger.info(`Email sent to ${to}: ${subject}`);
        return result;

      } catch (error) {
        logger.error('Error sending email:', error);
        throw error;
      }
    },

    async sendSMS(to, message) {
      try {
        const result = await notificationStrategy.execute('sms', {
          to,
          message
        });

        logger.info(`SMS sent to ${to}`);
        return result;

      } catch (error) {
        logger.error('Error sending SMS:', error);
        throw error;
      }
    },

    async sendPushNotification(id, title, body, data = {}) {
      try {
        const result = await notificationStrategy.execute('push', {
          id,
          title,
          body,
          data
        });

        logger.info(`Push notification sent to user ${id}: ${title}`);
        return result;

      } catch (error) {
        logger.error('Error sending push notification:', error);
        throw error;
      }
    },

    async sendInAppNotification(id, type, message, data = {}) {
      try {
        // Store notification in database
        const notification = {
          id,
          type,
          message,
          data,
          read: false,
          createdAt: new Date()
        };

        // Publish to real-time channel
        await publishToRabbit('events', 'notification.in-app', notification);

        logger.info(`In-app notification sent to user ${id}: ${type}`);
        return notification;

      } catch (error) {
        logger.error('Error sending in-app notification:', error);
        throw error;
      }
    },

    async sendBulkEmails(recipients, subject, template, commonData = {}) {
      try {
        const results = [];
        
        // Process in batches to avoid overwhelming the system
        const batchSize = 100;
        for (let i = 0; i < recipients.length; i += batchSize) {
          const batch = recipients.slice(i, i + batchSize);
          
          const batchPromises = batch.map(recipient => 
            this.sendEmail(
              recipient.email,
              subject,
              template,
              { ...commonData, ...recipient.data }
            ).catch(error => ({
              email: recipient.email,
              error: error.message
            }))
          );

          const batchResults = await Promise.all(batchPromises);
          results.push(...batchResults);
        }

        const successful = results.filter(r => !r.error).length;
        const failed = results.filter(r => r.error).length;

        logger.info(`Bulk email sent: ${successful} successful, ${failed} failed`);
        return { successful, failed, results };

      } catch (error) {
        logger.error('Error sending bulk emails:', error);
        throw error;
      }
    },

    async scheduleNotification(type, scheduledTime, data) {
      try {
        const delay = new Date(scheduledTime).getTime() - Date.now();
        
        if (delay <= 0) {
          throw new Error('Scheduled time must be in the future');
        }

        // Publish to delayed queue
        await publishToRabbit('events', 'notification.scheduled', {
          type,
          data,
          scheduledTime
        }, {
          headers: {
            'x-delay': delay
          }
        });

        logger.info(`Notification scheduled for ${scheduledTime}`);
        return { scheduled: true, scheduledTime };

      } catch (error) {
        logger.error('Error scheduling notification:', error);
        throw error;
      }
    },

    async getNotificationTemplates() {
      return {
        'welcome': {
          subject: 'Welcome to Our Platform!',
          template: 'welcome-email'
        },
        'email-verification': {
          subject: 'Verify Your Email Address',
          template: 'email-verification'
        },
        'password-reset': {
          subject: 'Password Reset Request',
          template: 'password-reset'
        },
        'order-confirmation': {
          subject: 'Order Confirmation',
          template: 'order-confirmation'
        },
        'order-shipped': {
          subject: 'Your Order Has Been Shipped!',
          template: 'order-shipped'
        },
        'order-delivered': {
          subject: 'Your Order Has Been Delivered',
          template: 'order-delivered'
        }
      };
    },

    async trackNotification(notificationId, event) {
      try {
        // Track notification events (sent, opened, clicked, etc.)
        await publishToKafka('notification-events', {
          notificationId,
          event,
          timestamp: new Date()
        });

        logger.info(`Notification event tracked: ${notificationId} - ${event}`);
        return true;

      } catch (error) {
        logger.error('Error tracking notification:', error);
        return false;
      }
    },

    async getNotificationStats(id, period = '7d') {
      // This would typically query a time-series database
      // For now, returning mock data
      return {
        sent: {
          email: 45,
          sms: 12,
          push: 78,
          inApp: 134
        },
        opened: {
          email: 32,
          push: 65,
          inApp: 120
        },
        clicked: {
          email: 28,
          push: 45,
          inApp: 98
        }
      };
    }
  };
};

module.exports = { createNotificationService };