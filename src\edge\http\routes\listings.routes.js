const ListingController = require('../controllers/listing.controller');

async function listingRoutes(fastify, options) {
  const listingController = new ListingController();

  // Listing CRUD routes
  fastify.post('/', {
    schema: {
      tags: ['listings'],
      summary: 'Create a new listing',
      description: 'Create a new rental listing',
  //    security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['CategoryID', 'Title', 'Description', 'Location', 'PricePerDay'],
        properties: {
          CategoryID: { type: 'string', format: 'uuid' },
          SubcategoryID: { type: 'string', format: 'uuid' },
          Title: { type: 'string', minLength: 3, maxLength: 200 },
          Description: { type: 'string', minLength: 10, maxLength: 5000 },
          Location: { type: 'string', minLength: 3, maxLength: 200 },
          Latitude: { type: 'number', minimum: -90, maximum: 90 },
          Longitude: { type: 'number', minimum: -180, maximum: 180 },
          PricePerDay: { type: 'number', minimum: 0 },
          PricePerWeek: { type: 'number', minimum: 0 },
          PricePerMonth: { type: 'number', minimum: 0 },
          StartDate: { type: 'string', format: 'date-time' },
          EndDate: { type: 'string', format: 'date-time' },
          SecurityDeposit: { type: 'number', minimum: 0, default: 0 },
          Condition: {
            type: 'string'},
          DeliveryOptions: {
            type: 'string'
          },
        }
      },
      response: {
        201: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            data: { type: 'object' }
          }
        },
        400: {
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            message: { type: 'string' },
            error: { type: 'string' }
          }
        }
      }
    },
    // preHandler: async (request, reply) => {
    //   try {
    //     await request.jwtVerify();
    //   } catch (err) {
    //     reply.send(err);
    //   }
    // },
    handler: listingController.createListing.bind(listingController)
  });

  fastify.get('/', {
    schema: {
      tags: ['listings'],
      summary: 'Get all listings with search and filters',
      description: 'Get all listings with optional search, filters and pagination',
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          categoryId: { type: 'string', format: 'uuid' },
          subcategoryId: { type: 'string', format: 'uuid' },
          location: { type: 'string' },
          startDate: { type: 'string', format: 'date-time' },
          endDate: { type: 'string', format: 'date-time' },
          minPrice: { type: 'number', minimum: 0 },
          maxPrice: { type: 'number', minimum: 0 },
          condition: { type: 'string' },
          search: { type: 'string' },
          sortBy: { type: 'string', default: 'CreatedAt' },
          sortOrder: { type: 'string', enum: ['ASC', 'DESC'], default: 'DESC' }
        }
      }
    },
    handler: listingController.getAllListings.bind(listingController)
  });



  fastify.get('/:id', {
    schema: {
      tags: ['listings'],
      summary: 'Get listing by ID',
      description: 'Get a specific listing by ID',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.getListingById.bind(listingController)
  });

  // Get related listings
  fastify.get('/:id/related', {
    schema: {
      tags: ['listings'],
      summary: 'Get related listings',
      description: 'Get listings related to a specific listing (same category/subcategory)',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      querystring: {
        type: 'object',
        properties: {
          limit: { type: 'integer', minimum: 1, maximum: 20, default: 6 }
        }
      }
    },
    handler: listingController.getRelatedListings.bind(listingController)
  });



  // Category routes
  fastify.get('/categories', {
    schema: {
      tags: ['categories'],
      summary: 'Get all categories',
      description: 'Get all categories with subcategories'
    },
    handler: listingController.getAllCategories.bind(listingController)
  });

  fastify.get('/categories/:id', {
    schema: {
      tags: ['categories'],
      summary: 'Get category by ID',
      description: 'Get a specific category with its subcategories',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.getCategoryById.bind(listingController)
  });

  // Update listing
  fastify.put('/:id', {
    schema: {
      tags: ['listings'],
      summary: 'Update listing',
      description: 'Update a listing by ID',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      },
      body: {
        type: 'object',
        properties: {
          CategoryID: { type: 'string', format: 'uuid' },
          SubcategoryID: { type: 'string', format: 'uuid' },
          Title: { type: 'string', minLength: 3, maxLength: 200 },
          Description: { type: 'string', minLength: 10, maxLength: 5000 },
          Location: { type: 'string', minLength: 3, maxLength: 200 },
          StartDate: { type: 'string', format: 'date-time' },
          EndDate: { type: 'string', format: 'date-time' },
          Latitude: { type: 'number', minimum: -90, maximum: 90 },
          Longitude: { type: 'number', minimum: -180, maximum: 180 },
          PricePerDay: { type: 'number', minimum: 0 },
          PricePerWeek: { type: 'number', minimum: 0 },
          PricePerMonth: { type: 'number', minimum: 0 },
          SecurityDeposit: { type: 'number', minimum: 0 },
          Condition: {
            type: 'object',
            properties: {
              new: { type: 'boolean' },
              good: { type: 'boolean' },
              used: { type: 'boolean' }
            }
          },
          DeliveryOptions: {
            type: 'object',
            properties: {
              pickup: { type: 'boolean' },
              homeDelivery: { type: 'boolean' }
            }
          }
        }
      }
    },
    handler: listingController.updateListing.bind(listingController)
  });

  // Delete listing
  fastify.delete('/:id', {
    schema: {
      tags: ['listings'],
      summary: 'Delete listing',
      description: 'Delete a listing by ID',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.deleteListing.bind(listingController)
  });

  // Get subcategories by category
  fastify.get('/categories/:id/subcategories', {
    schema: {
      tags: ['categories'],
      summary: 'Get subcategories by category',
      description: 'Get all subcategories for a specific category',
      params: {
        type: 'object',
        properties: {
          id: { type: 'string', format: 'uuid' }
        },
        required: ['id']
      }
    },
    handler: listingController.getSubcategoriesByCategory.bind(listingController)
  });
}

module.exports = listingRoutes;




