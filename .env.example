# Application Configuration
NODE_ENV=development
PORT=7000
APP_NAME=Rentle Backend API
APP_VERSION=1.0.0

# Database Configuration (Master - Write Operations)
DB_MASTER_HOST=localhost
DB_MASTER_PORT=5432
DB_MASTER_USERNAME=postgres
DB_MASTER_PASSWORD=your_password
DB_MASTER_DATABASE=rentle_db

# Database Configuration (Replica - Read Operations)
# For development, you can use the same database for both
DB_REPLICA_HOST=localhost
DB_REPLICA_PORT=5432
DB_REPLICA_USERNAME=postgres
DB_REPLICA_PASSWORD=your_password
DB_REPLICA_DATABASE=rentle_db

# Force database sync (creates/updates tables)
FORCE_DB_SYNC=true

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=24h

# CORS Configuration
CORS_ORIGIN=*

# Logging
LOG_LEVEL=info

# Swagger Documentation
SWAGGER_ENABLED=true

# Feature Flags
METRICS_ENABLED=true
HEALTH_CHECKS_ENABLED=true
SOCKETIO_ENABLED=true
GRPC_ENABLED=true
MQTT_ENABLED=false
CACHING_ENABLED=true
QUEUES_ENABLED=true
FILE_UPLOAD_ENABLED=true
EMAIL_SERVICE_ENABLED=false
SMS_SERVICE_ENABLED=false
PUSH_NOTIFICATIONS_ENABLED=false

# File Upload Configuration
UPLOAD_MAX_FILE_SIZE=10485760
UPLOAD_ALLOWED_MIME_TYPES=image/jpeg,image/png,image/gif,image/webp
MAX_IMAGES_PER_LISTING=10

# Cloudinary Configuration (for image uploads)
CLOUDINARY_CLOUD_NAME=your_cloud_name
CLOUDINARY_API_KEY=your_api_key
CLOUDINARY_API_SECRET=your_api_secret
CLOUDINARY_SECURE=true
CLOUDINARY_FOLDER=rentle/listings
CLOUDINARY_QUALITY=auto
CLOUDINARY_FORMAT=auto
CLOUDINARY_WIDTH=1200
CLOUDINARY_HEIGHT=800
CLOUDINARY_CROP=limit
