# Authentication Guide

Ab aap real user ID use kar sakte hain instead of hardcoded 'test-user-id'.

## How Authentication Works

1. **User Registration/Login** - User register ya login karta hai
2. **JWT Token** - Server JWT token return karta hai
3. **Protected Routes** - Token ko header mein send karna hota hai
4. **User ID** - Server automatically user ID extract kar leta hai

## Step-by-Step Usage

### 1. Register/Login User
```bash
# Register
POST /api/v1/auth/register
{
  "email": "<EMAIL>",
  "password": "Misbah@123",
  "fullName": "Misbah"
}

# Login
POST /api/v1/auth/login
{
  "email": "<EMAIL>",
  "password": "Misbah@123"
}
```

Response mein aapko JWT token milega:
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "actual-user-id-here",
      "email": "<EMAIL>",
      "fullName": "Misbah"
    }
  }
}
```

### 2. Use Token in Protected Routes
Token ko Authorization header mein send karein:

```bash
# Create Listing (Protected)
POST /api/v1/listings
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
{
  "title": "My Listing",
  "description": "Description here",
  "price": 100
}

# Update Listing (Protected)
PUT /api/v1/listings/:id
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
{
  "title": "Updated Title"
}

# Delete Listing (Protected)
DELETE /api/v1/listings/:id
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Get My Listings (Protected)
GET /api/v1/listings/user/my-listings
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Toggle Listing Availability (Protected)
PATCH /api/v1/listings/:id/toggle-availability
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Protected Routes List

### Requires Authentication:
- `POST /api/v1/listings` - Create listing
- `PUT /api/v1/listings/:id` - Update listing
- `DELETE /api/v1/listings/:id` - Delete listing
- `GET /api/v1/listings/user/my-listings` - Get user's listings
- `PATCH /api/v1/listings/:id/toggle-availability` - Toggle availability

### Public Routes (No Auth Required):
- `GET /api/v1/listings` - Get all listings
- `GET /api/v1/listings/:id` - Get single listing
- `GET /api/v1/listings/search` - Search listings
- `GET /api/v1/listings/categories` - Get categories

## Error Responses

### No Token:
```json
{
  "success": false,
  "message": "Authentication required",
  "error": "UNAUTHORIZED"
}
```

### Invalid Token:
```json
{
  "statusCode": 401,
  "error": "Unauthorized",
  "message": "Invalid token"
}
```

### Expired Token:
```json
{
  "statusCode": 401,
  "error": "Unauthorized", 
  "message": "Token expired"
}
```

## Frontend Integration

### JavaScript Example:
```javascript
// Store token after login
const token = loginResponse.data.token;
localStorage.setItem('authToken', token);

// Use token in API calls
const response = await fetch('/api/v1/listings', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
  },
  body: JSON.stringify({
    title: 'My Listing',
    description: 'Description',
    price: 100
  })
});
```

### React Example:
```javascript
// Create axios instance with interceptor
const api = axios.create({
  baseURL: '/api/v1'
});

api.interceptors.request.use((config) => {
  const token = localStorage.getItem('authToken');
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

// Use in components
const createListing = async (listingData) => {
  const response = await api.post('/listings', listingData);
  return response.data;
};
```

## Testing with Postman/Insomnia

1. **Login first** to get token
2. **Copy the token** from response
3. **Add Authorization header** in subsequent requests:
   - Type: Bearer Token
   - Token: paste_your_token_here

## Important Notes

- ✅ **Real User ID** ab automatically use hoti hai
- ✅ **No more hardcoded** 'test-user-id'
- ✅ **Proper ownership** validation
- ✅ **Security** implemented on all user-specific operations
- ⚠️ **Token expires** after 24 hours (configurable)
- ⚠️ **Always include** Authorization header for protected routes
