const { getDatabase } = require('../config/database.config');
const defineUserModel = require('./users.model');
const defineCategoryModel = require('./category.model');
const defineSubcategoryModel = require('./subcategory.model');
const defineListingModel = require('./listing.model');
const { logger } = require('../utils/logger');

let models = {};

// Initialize models across master and replica DBs
const initializeModels = async () => {
  console.log('🚀 Initializing models...');

  const masterDb = getDatabase('write');
  const replicaDb = getDatabase('read');

  console.log('✅ Retrieved database connections (master & replica)');

  // Define models for write (master)
  console.log('📦 Defining write models...');
  models.User = defineUserModel(masterDb);
  models.Category = defineCategoryModel(masterDb);
  models.Subcategory = defineSubcategoryModel(masterDb);
  models.Listing = defineListingModel(masterDb);

  // Define models for read (replica)
  console.log('📦 Defining read models...');
  models.UserRead = defineUserModel(replicaDb);
  models.CategoryRead = defineCategoryModel(replicaDb);
  models.SubcategoryRead = defineSubcategoryModel(replicaDb);
  models.ListingRead = defineListingModel(replicaDb);

  // Setup associations (write)
  console.log('🔗 Setting up write associations...');
  Object.keys(models).forEach(modelName => {
    if (models[modelName].associate && !modelName.endsWith('Read')) {
      console.log(`↪️ Associating (write): ${modelName}`);
      logger.debug(`Associating write model: ${modelName}`);
      models[modelName].associate(models);
    }
  });

  // Associate read models
  console.log('🔗 Setting up read associations...');
  const readModels = {
    User: models.UserRead,
    Category: models.CategoryRead,
    Subcategory: models.SubcategoryRead,
    Listing: models.ListingRead,
  };

  Object.keys(readModels).forEach(modelName => {
    if (readModels[modelName].associate) {
      console.log(`↪️ Associating (read): ${modelName}Read`);
      logger.debug(`Associating read model: ${modelName}Read`);
      readModels[modelName].associate(readModels);
    }
  });

  // Sync all models in development
  if (process.env.NODE_ENV === 'development') {
    logger.info('🔁 Syncing models (development only)...');
    console.log('🔁 Starting model sync in development mode...');

    for (const [modelName, model] of Object.entries(models)) {
      try {
        console.log(`⏳ Syncing model: ${modelName}`);
        logger.debug(`🔄 Starting sync for: ${modelName}`);

        await model.sync({ alter: true });

        console.log(`✅ Synced model: ${modelName}`);
        logger.debug(`✅ Successfully synced: ${modelName}`);
      } catch (error) {
        console.error(`❌ Error syncing model: ${modelName}`, error);
        logger.error(`❌ Failed to sync ${modelName}: ${error.message}`, { stack: error.stack });
      }
    }

    console.log('✅ All models synced successfully.');
    logger.info('✅ Model sync complete');
  }

  logger.info(`🗃️ Models initialized: ${Object.keys(models).length}`);
  console.log(`🗃️ Total models initialized: ${Object.keys(models).length}`);

  return models;
};

// 🔄 Access models based on read/write operation
const getModels = (operation = 'read') => {
  const writeOps = ['create', 'update', 'delete', 'upsert', 'bulkCreate', 'bulkUpdate', 'bulkDelete', 'write'];
  const isWrite = writeOps.includes(operation);

  console.log(`🔍 getModels called with operation: ${operation} | isWrite: ${isWrite}`);

  return isWrite
    ? {
        User: models.User,
        Category: models.Category,
        Subcategory: models.Subcategory,
        Listing: models.Listing,
      }
    : {
        User: models.UserRead,
        Category: models.CategoryRead,
        Subcategory: models.SubcategoryRead,
        Listing: models.ListingRead,
      };
};

/**
 * Get a single model by name
 * @param {string} modelName 
 */
const getModel = (modelName) => {
  console.log(`🔍 getModel called with modelName: ${modelName}`);
  if (!models[modelName]) {
    console.error(`❌ Model '${modelName}' not found`);
    throw new Error(`Model '${modelName}' not found`);
  }
  return models[modelName];
};

// 🧹 Optional: Clear model cache for testing/hot reload
const clearModelsCache = () => {
  console.log('🧹 Clearing model cache...');
  models = {};
  logger.debug('🧹 Cleared model cache');
};

// 🧾 Optional: Model stats
const getModelStats = () => {
  return {
    total: Object.keys(models).length,
    names: Object.keys(models)
  };
};

module.exports = {
  initializeModels,
  getModels,
  getModel,
  clearModelsCache,
  getModelStats,
  models
};

// const fs = require('fs');
// const path = require('path');
// const { getDatabase } = require('../config/database.config');
// const { logger } = require('../utils/logger');
// const { DataTypes } = require('sequelize');

// let modelsCache = null;

// /**
//  * Dynamically loads all models in /models folder
//  * @param {Sequelize} sequelize
//  * @param {Object} options
//  * @returns {Object} models
//  */
// const initializeModels = (sequelize, options = {}) => {
//   // Get the database instance if not provided
//   if (!sequelize) {
//     sequelize = getDatabase('write'); // This will get masterDb after it's initialized
//     if (!sequelize) {
//       logger.error('❌ Sequelize instance is null');
//       throw new Error('Sequelize instance is null');
//     }
//   }

  

//   if (modelsCache && !options.skipCache) {
//     logger.debug('📋 Returning cached models');
//     return modelsCache;
//   }

//   try {
//     logger.info('🗃️ Auto-loading models...');

//     const models = {};
//     const modelsDir = __dirname;
//     logger.debug('Models directory:', modelsDir);
//     // Specify the desired order for model loading (filenames)
//     const modelLoadOrder = [
//       'users.model.js',
//       'category.model.js',
//       'subcategory.model.js',
//       'listing.model.js',
//       // 'listing-form.model.js',
//       // 'chat.model.js',
//       // 'message.model.js',
//       // Add more model filenames here if needed
//     ];

//     // Load models in the specified order first
//     const loadedFiles = new Set();
//     logger.debug('Loading models in specified order: ' + modelLoadOrder.join(', '));
//     modelLoadOrder.forEach(file => {
//       const filePath = path.join(modelsDir, file);
//       logger.debug(`Checking for file: ${filePath}`);
//       if (fs.existsSync(filePath)) {
//         logger.debug(`File found: ${filePath}`);
//         const defineModel = require(filePath);
//         const model = defineModel(sequelize, DataTypes);
//         models[model.name] = model;
//         loadedFiles.add(file);
//         logger.debug(`✅ Loaded model (ordered): ${model.name}`);
//       }
//     });

//     // Load any remaining models
//     fs.readdirSync(modelsDir)
//       .filter(file => file.endsWith('.model.js') && !loadedFiles.has(file))
//       .forEach(file => {
//         const defineModel = require(path.join(modelsDir, file));
//         const model = defineModel(sequelize);
//         models[model.name] = model;
//         logger.debug(`✅ Loaded model: ${model.name}`);
//       });

//     // Auto-associate if associate() is defined
//     Object.values(models).forEach(model => {
//       if (typeof model.associate === 'function') {
//         logger.debug(`Associating model: ${model.name}`);
//         model.associate(models);
//         logger.debug(`🔗 Associated model: ${model.name}`);
//       }
//     });

//     modelsCache = models;

//     // Auto-sync in dev
//     if (process.env.NODE_ENV === 'development' && !options.skipCache) {
//       logger.debug('Development environment detected, starting model sync');
//       (async () => {
//         for (const model of Object.values(models)) {
//           logger.debug(`Syncing model: ${model.name}`);
//           await model.sync({ alter: true });
//           logger.debug(`Model synced: ${model.name}`);
//         }
//         logger.info('🔁 Models synced (development only)');
//       })();
//     }

//     logger.info(`✅ Initialized ${Object.keys(models).length} models`);
//     return models;

//   } catch (err) {
//     logger.error('❌ Model loading failed:', err);
//     throw err;
//   }
// };

// /**
//  * Returns a specific model by name
//  */
// const getModel = (modelName) => {
//   console.log(`Attempting to get model: ${modelName}`);
//   const models = initializeModels();
//   if (!models[modelName]) {
//     console.log(`Model '${modelName}' not found`);
//     throw new Error(`Model '${modelName}' not found`);
//   }
//   return models[modelName];
// };

// /**
//  * Clears the models cache (for testing/dev reloads)
//  */
// const clearModelsCache = () => {
//   modelsCache = null;
//   logger.debug('🗑️ Model cache cleared');
// };

// /**
//  * Returns current model stats
//  */
// const getModelStats = () => {
//   if (!modelsCache) return { initialized: false };

//   const stats = {
//     initialized: true,
//     totalModels: Object.keys(modelsCache).length,
//     models: Object.keys(modelsCache),
//     associations: {}
//   };

//   Object.entries(modelsCache).forEach(([name, model]) => {
//     stats.associations[name] = {
//       hasMany: Object.keys(model.associations).filter(k => model.associations[k].associationType === 'HasMany'),
//       belongsTo: Object.keys(model.associations).filter(k => model.associations[k].associationType === 'BelongsTo'),
//       manyToMany: Object.keys(model.associations).filter(k => model.associations[k].associationType === 'BelongsToMany')
//     };
//   });

//   return stats;
// };

// module.exports = {
//   initializeModels,
//   getModel,
//   clearModelsCache,
//   getModelStats
// };

