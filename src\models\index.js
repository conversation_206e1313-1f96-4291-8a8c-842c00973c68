const { getDatabase } = require('../config/database.config');

// Import model definitions
const defineUserModel = require('./users.model');
const defineCategoryModel = require('./category.model');
const defineSubcategoryModel = require('./subcategory.model');
const defineListingModel = require('./listing.model');

let models = {};

const initializeModels = () => {
  const masterDb = getDatabase('write');
  const replicaDb = getDatabase('read');

  // Initialize models on both databases
  models.User = defineUserModel(masterDb);
  models.Category = defineCategoryModel(masterDb);
  models.Subcategory = defineSubcategoryModel(masterDb);
  models.Listing = defineListingModel(masterDb);

  // Read-only models for replica
  models.UserRead = defineUserModel(replicaDb);
  models.CategoryRead = defineCategoryModel(replicaDb);
  models.SubcategoryRead = defineSubcategoryModel(replicaDb);
  models.ListingRead = defineListingModel(replicaDb);

  // Setup associations for write models
  Object.keys(models).forEach(modelName => {
    if (models[modelName].associate && !modelName.endsWith('Read')) {
      models[modelName].associate(models);
    }
  });

  // Setup associations for read models
  const readModels = {
    User: models.UserRead,
    Category: models.CategoryRead,
    Subcategory: models.SubcategoryRead,
    Listing: models.ListingRead
  };

  Object.keys(readModels).forEach(modelName => {
    if (readModels[modelName].associate) {
      readModels[modelName].associate(readModels);
    }
  });

  return models;
};

const getModels = (operation = 'read') => {
  const writeOperations = ['create', 'update', 'delete', 'upsert', 'bulkCreate', 'bulkUpdate', 'bulkDelete', 'write'];
  const isWrite = writeOperations.includes(operation);

  if (isWrite) {
    return {
      User: models.User,
      Category: models.Category,
      Subcategory: models.Subcategory,
      Listing: models.Listing
    };
  } else {
    return {
      User: models.UserRead,
      Category: models.CategoryRead,
      Subcategory: models.SubcategoryRead,
      Listing: models.ListingRead
    };
  }
};

module.exports = {
  initializeModels,
  getModels,
  models
};
