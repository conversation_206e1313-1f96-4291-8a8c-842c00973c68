const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { publishToRabbit, publishToKafka } = require('../config/queue.config');
const { logger } = require('../utils/logger');

const createUserService = ({ userRepository }) => {
  return {
    async createUser(userData) {
      try {
        console.log('user data (service)', userData)

        // Check if user already exists
        const existingEmail = await userRepository.findByEmail(userData.email);
        if (existingEmail) {
          throw new Error('Email already registered');
        }

        // const existingUsername = await userRepository.findByUsername(userData.username);
        // if (existingUsername) {
        //   throw new Error('Username already taken');
        // }

        // Generate email verification token
        const emailVerificationToken = uuidv4();

        // Create user
        const user = await userRepository.create({
          ...userData,
          emailVerificationToken
        });

        // Publish events
        await publishToRabbit('events', 'user.created', {
          id: user.id,
          email: user.email,
          timestamp: new Date()
        });

        await publishToKafka('user-events', {
          type: 'USER_CREATED',
          data: { id: user.id, email: user.email },
          timestamp: new Date()
        });

        // Send verification email (async)
        await publishToRabbit('events', 'notification.send-email', {
          type: 'email-verification',
          to: user.email,
          data: {
            name: user.fullName,
            verificationLink: `${process.env.APP_URL}/verify-email?token=${emailVerificationToken}`
          }
        });

        logger.info(`User created successfully: ${user.id}`);
        return user;

      } catch (error) {
        logger.error('Error creating user:', error);
        throw error;
      }
    },

    async getUserById(id) {
      const user = await userRepository.findById(id, { skipCache: true });
      if (!user) {
        throw new Error('User not found');
      }
      return user;
    },

    async getUserByEmail(email) {
      const user = await userRepository.findByEmail(email);
      if (!user) {
        throw new Error('User not found');
      }
      return user;
    },

    async updateUser(id, validatedData) {
      try {
        const { fullName, phone, cnic } = validatedData;
        const safeData = { fullName, phone, cnic };

        const user = await userRepository.update(id, { fullName, phone, cnic });


        // Publish update event
        await publishToRabbit('events', 'user.updated', {
          id: user.id,
          changes: Object.keys(safeData),
          timestamp: new Date()
        });

        await publishToKafka('user-events', {
          type: 'USER_UPDATE',
          data: safeData,
          timestamp: new Date()
        });

        logger.info(`User updated successfully: ${id}`);
        return user;

      } catch (error) {
        logger.error(`Error updating user: ${error}`);
        throw error;
      }
    },


    async deleteUser(id) {
      try {
        // Soft delete
        await userRepository.delete(id);

        // Publish deletion event
        await publishToRabbit('events', 'user.deleted', {
          id,
          timestamp: new Date()
        });

        logger.info(`User deleted successfully: ${id}`);
        return true;

      } catch (error) {
        logger.error('Error deleting user:', error);
        throw error;
      }
    },

    async verifyEmail(id, token) {
      try {
        const user = await userRepository.verifyEmail(id, token);

        // Publish verification event
        await publishToRabbit('events', 'user.email-verified', {
          id: user.id,
          timestamp: new Date()
        });

        logger.info(`Email verified for user: ${id}`);
        return user;

      } catch (error) {
        logger.error('Error verifying email:', error);
        throw error;
      }
    },

    async requestPasswordReset(email) {
      try {
        const user = await userRepository.findByEmail(email);
        if (!user) {
          // Don't reveal if email exists
          return true;
        }

        const resetToken = uuidv4();
        const expires = new Date();
        expires.setHours(expires.getHours() + 1); // 1 hour expiry

        await userRepository.setPasswordResetToken(user.id, resetToken, expires);

        // Send reset email
        await publishToRabbit('events', 'notification.send-email', {
          type: 'password-reset',
          to: user.email,
          data: {
            name: user.fullName,
            resetLink: `${process.env.APP_URL}/reset-password?token=${resetToken}`
          }
        });

        logger.info(`Password reset requested for user: ${user.id}`);
        return true;

      } catch (error) {
        logger.error('Error requesting password reset:', error);
        throw error;
      }
    },

    async resetPassword(id, token, newPassword) {
      try {
        const user = await userRepository.resetPassword(id, token, newPassword);

        // Publish password reset event
        await publishToRabbit('events', 'user.password-reset', {
          userId: user.id,
          timestamp: new Date()
        });

        // Send confirmation email
        await publishToRabbit('events', 'notification.send-email', {
          type: 'password-reset-confirmation',
          to: user.email,
          data: {
            name: user.fullName
          }
        });

        logger.info(`Password reset for user: ${id}`);
        return user;

      } catch (error) {
        logger.error('Error resetting password:', error);
        throw error;
      }
    },

async changePassword(id, currentPassword, newPassword, cache) {
  try {
    console.log("🔐 Password Change Request:", { id, currentPassword, newPassword });

    const user = await userRepository.findById(id, {
      skipCache: true,
      attributes: {
        include: ['password']
      }
    });

    if (!user) {
      throw new Error('User not found');
    }

    const isValid = await user.comparePassword(currentPassword);
    if (!isValid) {
      throw new Error('Invalid current password');
    }

    // Update password (it will be hashed in beforeUpdate hook)
    await userRepository.update(id, { password: newPassword });

    // Clear cache for user
    // if (cache) {
    //   await cache.delete(`user:email:${user.email}`);
    //   await cache.delete(`user:${user.id}`);
    // }

    // Optional: publish to RabbitMQ
    try {
      await publishToRabbit('events', 'user.password-changed', {
        id: user.id,
        timestamp: new Date()
      });
    } catch (rabbitError) {
      logger.warn('⚠️ RabbitMQ publish failed:', rabbitError.message);
    }

    logger.info(`✅ Password changed for user: ${id}`);
    return {
      success: true,
      message: 'Password changed successfully'
    };

  } catch (error) {
    logger.error(`❌ Error changing password for user ${id}:`, error);
    throw error;
  }
},





    async listUsers(filters = {}, options = {}) {
      const { page = 1, limit = 20, sort = 'createdAt', order = 'DESC' } = options;

      const result = await userRepository.findAndCountAll(filters, {
        page,
        limit,
        order: [[sort, order]],
        attributes: { exclude: ['password'] }
      });

      return {
        users: result.rows,
        total: result.count,
        page,
        totalPages: Math.ceil(result.count / limit)
      };
    },

    async searchUsers(searchTerm, options = {}) {
      const users = await userRepository.searchUsers(searchTerm, options);
      return users;
    },

    async getUserStatistics() {
      const stats = await userRepository.getUserStatistics();
      return stats;
    },

    async updateUserPreferences(id, preferences) {
      try {
        const user = await userRepository.findById(id);
        if (!user) {
          throw new Error('User not found');
        }

        const updatedPreferences = {
          ...user.preferences,
          ...preferences
        };

        await userRepository.update(id, { preferences: updatedPreferences });

        logger.info(`Preferences updated for user: ${id}`);
        return updatedPreferences;

      } catch (error) {
        logger.error('Error updating preferences:', error);
        throw error;
      }
    },

    async bulkCreateUsers(usersData) {
      try {
        // Validate unique emails and usernames
        const emails = usersData.map(u => u.email);
        const usernames = usersData.map(u => u.username);

        const existingUsers = await userRepository.findAll({
          $or: [
            { email: { $in: emails } },
            { username: { $in: usernames } }
          ]
        });

        if (existingUsers.length > 0) {
          throw new Error('Some users already exist');
        }

        // Add verification tokens
        const usersWithTokens = usersData.map(user => ({
          ...user,
          emailVerificationToken: uuidv4()
        }));

        const users = await userRepository.bulkCreate(usersWithTokens);

        // Publish bulk creation event
        await publishToKafka('user-events', {
          type: 'USERS_BULK_CREATED',
          data: { count: users.length },
          timestamp: new Date()
        });

        logger.info(`Bulk created ${users.length} users`);
        return users;

      } catch (error) {
        logger.error('Error bulk creating users:', error);
        throw error;
      }
    },

    async updateLastLogin(id) {
      try {
        await userRepository.updateLastLogin(id)
        logger.info(`User ${id} last login updated`)
      } catch (error) {
        logger.error('Failed to update the last login', error.message)
      }
    }
  };
};

module.exports = { createUserService };