const bcrypt = require('bcrypt');
const { v4: uuidv4 } = require('uuid');
const { publishToRabbit, publishToKafka } = require('../config/queue.config');
const { logger } = require('../utils/logger');

const createUserService = ({ userRepository }) => {
  return {
    async createUser(userData) {
      try {
        
        // Check if user already exists
        const existingEmail = await userRepository.findByEmail(userData.email);
        if (existingEmail) {
          throw new Error('Email already registered');
        }

        const existingUsername = await userRepository.findByUsername(userData.username);
        if (existingUsername) {
          throw new Error('Username already taken');
        }

        // Generate email verification token
        const emailVerificationToken = uuidv4();

        // Create user
        const user = await userRepository.create({
          ...userData,
          emailVerificationToken
        });

        // Publish events
        await publishToRabbit('events', 'user.created', {
          userId: user.id,
          email: user.email,
          timestamp: new Date()
        });

        await publishToKafka('user-events', {
          type: 'USER_CREATED',
          data: { userId: user.id, email: user.email },
          timestamp: new Date()
        });

        // Send verification email (async)
        await publishToRabbit('events', 'notification.send-email', {
          type: 'email-verification',
          to: user.email,
          data: {
            name: user.firstName,
            verificationLink: `${process.env.APP_URL}/verify-email?token=${emailVerificationToken}`
          }
        });

        logger.info(`User created successfully: ${user.id}`);
        return user;

      } catch (error) {
        logger.error('Error creating user:', error);
        throw error;
      }
    },

    async getUserById(userId) {
      const user = await userRepository.findById(userId, { skipCache: true });
      if (!user) {
        throw new Error('User not found');
      }
      return user;
    },

    async getUserByEmail(email) {
      const user = await userRepository.findByEmail(email);
      if (!user) {
        throw new Error('User not found');
      }
      return user;
    },

    async updateUser(userId, updateData) {
      try {
        // Prevent updating sensitive fields directly
        const { password, email, emailVerified, ...safeData } = updateData;
        console.log('got data to update', updateData)
        // If email is being updated, require verification
        if (email && email !== (await userRepository.findById(userId)).email) {
          safeData.email = email;
          safeData.emailVerified = false;
          safeData.emailVerificationToken = uuidv4();
        }
        const user = await userRepository.update(userId, safeData);

        // Publish update event
        await publishToRabbit('events', 'user.updated', {
          userId: user.id,
          changes: Object.keys(safeData),
          timestamp: new Date()
        });

        logger.info(`User updated successfully: ${userId}`);
        return user;

      } catch (error) {
        logger.error(`Error updating user: ${error}`);
        throw error;
      }
    },

    async deleteUser(userId) {
      try {
        // Soft delete
        await userRepository.delete(userId);

        // Publish deletion event
        await publishToRabbit('events', 'user.deleted', {
          userId,
          timestamp: new Date()
        });

        logger.info(`User deleted successfully: ${userId}`);
        return true;

      } catch (error) {
        logger.error('Error deleting user:', error);
        throw error;
      }
    },

    async verifyEmail(userId, token) {
      try {
        const user = await userRepository.verifyEmail(userId, token);

        // Publish verification event
        await publishToRabbit('events', 'user.email-verified', {
          userId: user.id,
          timestamp: new Date()
        });

        logger.info(`Email verified for user: ${userId}`);
        return user;

      } catch (error) {
        logger.error('Error verifying email:', error);
        throw error;
      }
    },

    async requestPasswordReset(email) {
      try {
        const user = await userRepository.findByEmail(email);
        if (!user) {
          // Don't reveal if email exists
          return true;
        }

        const resetToken = uuidv4();
        const expires = new Date();
        expires.setHours(expires.getHours() + 1); // 1 hour expiry

        await userRepository.setPasswordResetToken(user.id, resetToken, expires);

        // Send reset email
        await publishToRabbit('events', 'notification.send-email', {
          type: 'password-reset',
          to: user.email,
          data: {
            name: user.firstName,
            resetLink: `${process.env.APP_URL}/reset-password?token=${resetToken}`
          }
        });

        logger.info(`Password reset requested for user: ${user.id}`);
        return true;

      } catch (error) {
        logger.error('Error requesting password reset:', error);
        throw error;
      }
    },

    async resetPassword(userId, token, newPassword) {
      try {
        const user = await userRepository.resetPassword(userId, token, newPassword);

        // Publish password reset event
        await publishToRabbit('events', 'user.password-reset', {
          userId: user.id,
          timestamp: new Date()
        });

        // Send confirmation email
        await publishToRabbit('events', 'notification.send-email', {
          type: 'password-reset-confirmation',
          to: user.email,
          data: {
            name: user.firstName
          }
        });

        logger.info(`Password reset for user: ${userId}`);
        return user;

      } catch (error) {
        logger.error('Error resetting password:', error);
        throw error;
      }
    },

    async changePassword(userId, currentPassword, newPassword) {
      try {
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        // Verify current password
        const isValid = await user.comparePassword(currentPassword);
        if (!isValid) {
          throw new Error('Invalid current password');
        }

        // Update password
        await userRepository.update(userId, { password: newPassword });

        // Publish event
        await publishToRabbit('events', 'user.password-changed', {
          userId: user.id,
          timestamp: new Date()
        });

        logger.info(`Password changed for user: ${userId}`);
        return true;

      } catch (error) {
        logger.error('Error changing password:', error);
        throw error;
      }
    },

    async listUsers(filters = {}, options = {}) {
      const { page = 1, limit = 20, sort = 'createdAt', order = 'DESC' } = options;

      const result = await userRepository.findAndCountAll(filters, {
        page,
        limit,
        order: [[sort, order]],
        attributes: { exclude: ['password'] }
      });

      return {
        users: result.rows,
        total: result.count,
        page,
        totalPages: Math.ceil(result.count / limit)
      };
    },

    async searchUsers(searchTerm, options = {}) {
      const users = await userRepository.searchUsers(searchTerm, options);
      return users;
    },

    async getUserStatistics() {
      const stats = await userRepository.getUserStatistics();
      return stats;
    },

    async updateUserPreferences(userId, preferences) {
      try {
        const user = await userRepository.findById(userId);
        if (!user) {
          throw new Error('User not found');
        }

        const updatedPreferences = {
          ...user.preferences,
          ...preferences
        };

        await userRepository.update(userId, { preferences: updatedPreferences });

        logger.info(`Preferences updated for user: ${userId}`);
        return updatedPreferences;

      } catch (error) {
        logger.error('Error updating preferences:', error);
        throw error;
      }
    },

    async bulkCreateUsers(usersData) {
      try {
        // Validate unique emails and usernames
        const emails = usersData.map(u => u.email);
        const usernames = usersData.map(u => u.username);

        const existingUsers = await userRepository.findAll({
          $or: [
            { email: { $in: emails } },
            { username: { $in: usernames } }
          ]
        });

        if (existingUsers.length > 0) {
          throw new Error('Some users already exist');
        }

        // Add verification tokens
        const usersWithTokens = usersData.map(user => ({
          ...user,
          emailVerificationToken: uuidv4()
        }));

        const users = await userRepository.bulkCreate(usersWithTokens);

        // Publish bulk creation event
        await publishToKafka('user-events', {
          type: 'USERS_BULK_CREATED',
          data: { count: users.length },
          timestamp: new Date()
        });

        logger.info(`Bulk created ${users.length} users`);
        return users;

      } catch (error) {
        logger.error('Error bulk creating users:', error);
        throw error;
      }
    },

    async updateLastLogin(userId) {
      try {
        await userRepository.updateLastLogin(userId)
        logger.info(`User ${userId} last login updated`)
      } catch (error) {
        logger.error('Failed to update the last login', error.message)
      }
    }
  };
};

module.exports = { createUserService };