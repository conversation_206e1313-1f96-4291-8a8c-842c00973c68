const fastify = require('fastify');
const cors = require('@fastify/cors');
const helmet = require('@fastify/helmet');
const jwt = require('@fastify/jwt');
const rateLimit = require('@fastify/rate-limit');
const swagger = require('@fastify/swagger');
const swaggerUi = require('@fastify/swagger-ui');
const { Server } = require('socket.io');
const { createAdapter } = require('@socket.io/redis-adapter');

const { logger } = require('./utils/logger');
const { authHook } = require('./hooks/auth.hook');
const { errorHook } = require('./hooks/error.hook');
const { loggingHook, contextHook, performanceHook, onSendHook } = require('./hooks/logging.hook');
const { healthRoutes } = require('./monitoring/health');
const { metricsPlugin } = require('./monitoring/prometheus');

const createApp = async (container) => {
  const app = fastify({
    logger: {
      level: process.env.LOG_LEVEL || 10, // Use environment variable for log level
      transport: {
        target: 'pino-pretty', // Pretty-print logs in development
        options: {
          translateTime: 'SYS:yyyy-mm-dd HH:MM:ss.l o',
          ignore: 'pid,hostname',
        },
      },
    }, // Use Fastify's built-in logger with configuration,
    trustProxy: true,
    requestIdHeader: 'x-request-id',
    requestIdLogLabel: 'requestId',
    disableRequestLogging: false,
    bodyLimit: 10485760, // 10MB
  });

  // Decorate with custom logger for compatibility with existing code
  app.decorate('customLogger', logger);

  // Register plugins
  await app.register(cors, {
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true,
  });

  await app.register(helmet, {
    contentSecurityPolicy: false,
  });

  await app.register(jwt, {
    secret: process.env.JWT_SECRET,
  });

  await app.register(rateLimit, {
    max: 100,
    timeWindow: '1 minute',
  });

  // Register Swagger and Swagger UI separately
  await app.register(swagger, {
    openapi: {
      openapi: '3.0.3',
      info: {
        title: 'Enterprise API',
        description: 'Enterprise Node.js API documentation',
        version: '1.0.0',
      },
      servers: [
        { url: `http://localhost:${process.env.PORT || '7000'}`, description: 'Development server' },
      ],
      components: {
        securitySchemes: {
          bearerAuth: {
            type: 'http',
            scheme: 'bearer',
            bearerFormat: 'JWT',
          },
        },
      },
      tags: [
        { name: 'users', description: 'User operations' },
        { name: 'orders', description: 'Order operations' },
        { name: 'auth', description: 'Authentication' },
      ],
    },
  });

  await app.register(swaggerUi, {
    routePrefix: '/documentation',
    uiConfig: {
      docExpansion: 'list',
      deepLinking: false,
    },
    staticCSP: true,
    transformStaticCSP: (header) => {
      return header + "; connect-src 'self' http://localhost:7000";
    },
  });

  // Register custom plugins
  await app.register(metricsPlugin);
  await app.register(healthRoutes);

  // Setup hooks
  app.addHook('onRequest', loggingHook);
  app.addHook('onRequest', contextHook);
  app.addHook('onRequest', performanceHook);
  app.addHook('preHandler', authHook);
  app.addHook('onSend', onSendHook);
  app.addHook('onError', errorHook);

  // Decorate with container
  app.decorate('container', container);

  // Setup Socket.io
  const io = new Server(app.server, {
    cors: {
      origin: process.env.CORS_ORIGIN || '*',
      methods: ['GET', 'POST'],
    },
  });
  io.jwt = app.jwt
  // Setup Redis adapter for Socket.io
  const { getRedis } = require('./config/cache.config');
  const pubClient = getRedis();
  const subClient = pubClient.duplicate();

  // await Promise.all([pubClient.connect(), subClient.connect()]);

  // Verify Redis clients are ready (no need to call connect())
  if (pubClient.status !== 'ready' || subClient.status !== 'ready') {
    await Promise.all([
      pubClient.status === 'ready' ? Promise.resolve() : new Promise((resolve) => pubClient.once('ready', resolve)),
      subClient.status === 'ready' ? Promise.resolve() : new Promise((resolve) => subClient.once('ready', resolve)),
    ]);
  }

  io.adapter(createAdapter(pubClient, subClient));

  // Attach Socket.io handlers
  require('./edge/socketio/server')(io, container);

  app.decorate('io', io);

  // Register routes
  const routeModules = ['auth', 'users', 'listings'];

  for (const module of routeModules) {
    try {
      const routes = require(`./edge/http/routes/${module}.routes`);
      await app.register(routes, { prefix: `/api/v1/${module}` });
      logger.info(`✅ Registered routes for ${module}`);
    } catch (error) {
      logger.error(`Route module ${module} not found: ${error.message}`);
    }
  }

  // 404 handler
  app.setNotFoundHandler((request, reply) => {
    reply.code(404).send({
      error: 'Not Found',
      message: 'Route not found',
      statusCode: 404,
    });
  });

  return app;
};

module.exports = { createApp };