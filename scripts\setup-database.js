#!/usr/bin/env node

/**
 * Database Setup Script
 * This script initializes the database and creates all tables
 */

require('dotenv').config();
const { logger } = require('../src/utils/logger');

async function setupDatabase() {
  try {
    logger.info('🚀 Starting database setup...');

    // Initialize database connections
    const { initializeDatabase } = require('../src/config/database.config');
    await initializeDatabase();
    logger.info('✅ Database connections established');

    // Initialize and sync models
    const { initializeModels, syncModels } = require('../src/models');
    
    logger.info('🗃️ Initializing models...');
    const models = initializeModels();
    logger.info('✅ Models initialized');

    // Force sync to create all tables
    logger.info('🔄 Syncing database tables...');
    await syncModels(true); // Force sync to recreate tables
    logger.info('✅ Database tables created successfully');

    // Verify tables were created
    const { getDatabase } = require('../src/config/database.config');
    const db = getDatabase('write');
    
    const tables = await db.query("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'", {
      type: db.QueryTypes.SELECT
    });
    
    logger.info('📋 Created tables:');
    tables.forEach(table => {
      logger.info(`  - ${table.table_name}`);
    });

    logger.info('🎉 Database setup completed successfully!');
    process.exit(0);

  } catch (error) {
    logger.error('❌ Database setup failed:', error);
    console.error('Database setup failed:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
  logger.info('Setup interrupted by user');
  process.exit(0);
});

process.on('SIGTERM', () => {
  logger.info('Setup terminated');
  process.exit(0);
});

// Run setup
setupDatabase();
