const { getDatabase } = require('../config/database.config');
const { cache } = require('../config/cache.config');
const { logger } = require('../utils/logger');

const createBaseRepository = (model) => {
  const modelName = model.name.toLowerCase();

  const getCacheKey = (id) => `${modelName}:${id}`;
  const getListCacheKey = (params = {}) => `${modelName}:list:${JSON.stringify(params)}`;

  return {
    async findById(id, options = {}) {
      console.log('findById called with:', { id, options }); // Log input parameters
      console.log('modelName:', modelName);
      const cacheKey = getCacheKey(id);
      console.log("cache key", cacheKey);

      // Try cache first
      if (!options.skipCache) {
        console.log('Checking cache for key:', cacheKey);
        const cached = await cache.get(cacheKey);
        console.log('Cache result:', cached);
        if (cached) {
          logger.debug(`Cache hit for ${modelName}:${id}`);
          console.log('Building model from cached data:', cached);
          return model.build(cached, { isNewRecord: false });
        }
      }


      // Get from database
      const db = getDatabase('read');
      console.log('Database instance:', db);
      console.log('Model name:', model.name);
      console.log('Checking if model exists in db.models:', db.models[model.name]);
      const result = await db.models[model.name].findByPk(id, options);
      console.log('Database query result:', result);
            if (result && !options.skipCache) {
            console.log('Caching result with key:', cacheKey, 'TTL:', options.cacheTTL || 3600);
        await cache.set(cacheKey, result.toJSON(), options.cacheTTL || 3600);
      }

      return result;
    },

    async findOne(conditions, options = {}) {
      const db = getDatabase('read');
      return db.models[model.name].findOne({ where: conditions, ...options });
    },

    async findAll(conditions = {}, options = {}) {
      const cacheKey = getListCacheKey({ conditions, options });

      // Try cache first for list queries
      if (!options.skipCache && Object.keys(conditions).length > 0) {
        const cached = await cache.get(cacheKey);
        if (cached) {
          logger.debug(`Cache hit for ${modelName} list`);
          return cached;
        }
      }

      const db = getDatabase('read');
      const result = await db.models[model.name].findAll({ where: conditions, ...options });

      if (result && !options.skipCache && Object.keys(conditions).length > 0) {
        await cache.set(cacheKey, result, options.cacheTTL || 300);
      }

      return result;
    },

    async findAndCountAll(conditions = {}, options = {}) {
      const db = getDatabase('read');
      const { page = 1, limit = 20, ...otherOptions } = options;

      const offset = (page - 1) * limit;

      return db.models[model.name].findAndCountAll({
        where: conditions,
        limit,
        offset,
        ...otherOptions
      });
    },

    async create(data, options = {}) {

      const db = getDatabase('create');
      const result = await db.models[model.name].create(data, options);
      // Invalidate cache
      await this.invalidateCache();
      
      return result;
      
    },

    async bulkCreate(data, options = {}) {
      const db = getDatabase('bulkCreate');
      const result = await db.models[model.name].bulkCreate(data, options);

      // Invalidate cache
      await this.invalidateCache();

      return result;
    },

    async update(id, data, options = {}) {
      const db = getDatabase('update');

      const instance = await db.models[model.name].findByPk(id);

      if (!instance) {
        throw new Error(`${model.name} not found`);
      }

      const result = await instance.update(data, options);

      // Invalidate specific cache
      // await cache.delete(getCacheKey(id));
      // await this.invalidateCache();

      return result;
    },

    async updateWhere(conditions, data, options = {}) {
      const db = getDatabase('update');
      const result = await db.models[model.name].update(data, {
        where: conditions,
        ...options
      });

      // Invalidate cache
      await this.invalidateCache();

      return result;
    },

    async delete(id, options = {}) {
      const db = getDatabase('delete');
      const instance = await db.models[model.name].findByPk(id);

      if (!instance) {
        throw new Error(`${model.name} not found`);
      }

      const result = await instance.update({ isDeleted: true }, options);
      // const result = await instance.destroy(options);

      // Invalidate specific cache
      await cache.delete(getCacheKey(id));
      await this.invalidateCache();

      return result;
    },

    async deleteWhere(conditions, options = {}) {
      const db = getDatabase('delete');
      const result = await db.models[model.name].destroy({
        where: conditions,
        ...options
      });

      // Invalidate cache
      await this.invalidateCache();

      return result;
    },

    async count(conditions = {}, options = {}) {
      const db = getDatabase('read');
      return db.models[model.name].count({ where: conditions, ...options });
    },

    async exists(conditions) {
      const count = await this.count(conditions);
      return count > 0;
    },

    async invalidateCache() {
      try {
        await cache.deletePattern(`${modelName}:*`);
        logger.debug(`Cache invalidated for ${modelName}`);
      } catch (error) {
        logger.error(`Cache invalidation error for ${modelName}:`, error);
      }
    },

    async executeQuery(query, options = {}) {
      const db = getDatabase(options.type || 'read');
      return db.query(query, options);
    }
  };
};

module.exports = { createBaseRepository };