const ListingRepository = require('../repositories/listing.repository');
const CategoryRepository = require('../repositories/category.repository');
const { logger } = require('../utils/logger');

class ListingService {
  constructor() {
    this.listingRepository = new ListingRepository();
    this.categoryRepository = new CategoryRepository();
  }

  async createListing(listingData, userId) {
    try {
      // Validate category exists
      const category = await this.categoryRepository.findById(listingData.CategoryID, false);
      if (!category) {
        throw new Error('Category not found');
      }

      // Validate subcategory if provided
      if (listingData.SubcategoryID) {
        const subcategory = await this.categoryRepository.findSubcategoryById(listingData.SubcategoryID);
        if (!subcategory || subcategory.CategoryID !== listingData.CategoryID) {
          throw new Error('Invalid subcategory for the selected category');
        }
      }

      // Add lister ID
      listingData.ListerID = userId;

      // Create listing
      const listing = await this.listingRepository.create(listingData);

      // Return with associations
      return await this.listingRepository.findById(listing.ListingID);
    } catch (error) {
      logger.error('Error creating listing:', error);
      throw error;
    }
  }

  async getListingById(id, incrementView = false) {
    try {
      const listing = await this.listingRepository.findById(id);

      if (!listing) {
        throw new Error('Listing not found');
      }

      // Increment view count if requested
      if (incrementView) {
        await this.listingRepository.incrementViewCount(id);
        listing.ViewCount += 1;
      }

      return listing;
    } catch (error) {
      logger.error('Error getting listing by ID:', error);
      throw error;
    }
  }

  async getAllListings(options = {}) {
    try {
      return await this.listingRepository.findAll(options);
    } catch (error) {
      logger.error('Error getting all listings:', error);
      throw error;
    }
  }

  async getListingsByCategory(categoryId, options = {}) {
    try {
      // Validate category exists
      const category = await this.categoryRepository.findById(categoryId, false);
      if (!category) {
        throw new Error('Category not found');
      }

      return await this.listingRepository.findByCategory(categoryId, options);
    } catch (error) {
      logger.error('Error getting listings by category:', error);
      throw error;
    }
  }

  async getListingsBySubcategory(subcategoryId, options = {}) {
    try {
      // Validate subcategory exists
      const subcategory = await this.categoryRepository.findSubcategoryById(subcategoryId);
      if (!subcategory) {
        throw new Error('Subcategory not found');
      }

      return await this.listingRepository.findBySubcategory(subcategoryId, options);
    } catch (error) {
      logger.error('Error getting listings by subcategory:', error);
      throw error;
    }
  }

  async searchListings(searchOptions = {}) {
    try {
      const {
        search,
        categoryId,
        subcategoryId,
        location,
        minPrice,
        maxPrice,
        condition,
        sortBy = 'CreatedAt',
        sortOrder = 'DESC',
        page = 1,
        limit = 20
      } = searchOptions;

      const options = {
        search,
        categoryId,
        subcategoryId,
        location,
        minPrice,
        maxPrice,
        condition,
        sortBy,
        sortOrder,
        page,
        limit
      };

      return await this.listingRepository.findAll(options);
    } catch (error) {
      logger.error('Error searching listings:', error);
      throw error;
    }
  }

  async getRelatedListings(listingId, limit = 6) {
    try {
      return await this.listingRepository.findRelated(listingId, limit);
    } catch (error) {
      logger.error('Error getting related listings:', error);
      throw error;
    }
  }

  async getFeaturedListings(options = {}) {
    try {
      return await this.listingRepository.findAll({
        ...options,
        featured: true,
        sortBy: 'FeaturedUntil',
        sortOrder: 'DESC'
      });
    } catch (error) {
      logger.error('Error getting featured listings:', error);
      throw error;
    }
  }

  async updateListing(id, updateData, userId) {
    try {
      // Check if listing exists and belongs to user
      const existingListing = await this.listingRepository.findById(id, false);
      if (!existingListing) {
        throw new Error('Listing not found');
      }

      if (existingListing.ListerID !== userId) {
        throw new Error('Unauthorized to update this listing');
      }

      // Validate category if being updated
      if (updateData.CategoryID) {
        const category = await this.categoryRepository.findById(updateData.CategoryID, false);
        if (!category) {
          throw new Error('Category not found');
        }
      }

      // Validate subcategory if being updated
      if (updateData.SubcategoryID) {
        const subcategory = await this.categoryRepository.findSubcategoryById(updateData.SubcategoryID);
        if (!subcategory) {
          throw new Error('Subcategory not found');
        }

        const categoryId = updateData.CategoryID || existingListing.CategoryID;
        if (subcategory.CategoryID !== categoryId) {
          throw new Error('Invalid subcategory for the selected category');
        }
      }

      return await this.listingRepository.update(id, updateData);
    } catch (error) {
      logger.error('Error updating listing:', error);
      throw error;
    }
  }

  async deleteListing(id, userId) {
    try {
      // Check if listing exists and belongs to user
      const existingListing = await this.listingRepository.findById(id, false);
      if (!existingListing) {
        throw new Error('Listing not found');
      }

      if (existingListing.ListerID !== userId) {
        throw new Error('Unauthorized to delete this listing');
      }

      return await this.listingRepository.delete(id);
    } catch (error) {
      logger.error('Error deleting listing:', error);
      throw error;
    }
  }

  async getUserListings(userId, options = {}) {
    try {
      return await this.listingRepository.findByUser(userId, options);
    } catch (error) {
      logger.error('Error getting user listings:', error);
      throw error;
    }
  }

  async toggleListingAvailability(id, userId) {
    try {
      const listing = await this.listingRepository.findById(id, false);
      if (!listing) {
        throw new Error('Listing not found');
      }

      if (listing.ListerID !== userId) {
        throw new Error('Unauthorized to modify this listing');
      }

      return await this.listingRepository.update(id, {
        IsAvailable: !listing.IsAvailable
      });
    } catch (error) {
      logger.error('Error toggling listing availability:', error);
      throw error;
    }
  }

  // Category management methods
  async getAllCategories() {
    try {
      return await this.categoryRepository.findAll();
    } catch (error) {
      logger.error('Error getting categories:', error);
      throw error;
    }
  }

  async getCategoryById(id) {
    try {
      const category = await this.categoryRepository.findById(id);
      if (!category) {
        throw new Error('Category not found');
      }
      return category;
    } catch (error) {
      logger.error('Error getting category by ID:', error);
      throw error;
    }
  }

  async getSubcategoriesByCategory(categoryId) {
    try {
      const category = await this.categoryRepository.findById(categoryId, false);
      if (!category) {
        throw new Error('Category not found');
      }

      return await this.categoryRepository.findSubcategoriesByCategory(categoryId);
    } catch (error) {
      logger.error('Error getting subcategories:', error);
      throw error;
    }
  }
}

module.exports = ListingService;
