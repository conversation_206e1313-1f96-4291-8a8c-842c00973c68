const logger = require("../../../utils/logger");

const userController = {
    async getUser(request, reply) {
        try {

            const userId = request.params.userId || request.user.id;
            const container = request.server.container;

            const userMediator = container.resolve('userMediator')
            const validator = container.resolve('userValidator')
            // Validate UUID
            if (!validator.isUUID(userId)) {
                throw new Error('Invalid user ID format');
            }
            const result = await userMediator.getUser(userId)
            reply.send(result);

        } catch (error) {
            logger.error(`Fialed to get the user ${error}`)
            throw error
        }
    },
    async listUsers(request, reply) {
        try {

            const { page = 1, limit = 20, status, role, search } = request.query;

            // Build filters
            const filters = {};
            if (status) filters.status = status;
            if (role) filters.role = role;
            const userMediator = request.server.container.resolve('userMediator')
            const result = await userMediator.listUsers({ page, limit, status, role, search, filters })
            reply.send(result);

        } catch (error) {
            logger.error(`Failed to list users ${error}`)
            throw error
        }
    },
    async updateUser(request, reply) {
        try {

            const { userId } = request.params;
            const requesterId = request.user?.id;

            // Check authorization
            if (userId !== requesterId && request.user?.role !== 'admin') {
                throw new Error('Unauthorized to update this user');
            }
            const container = request.server.container;
            // const validator = container.resolve('userValidator')
            const userMediator = container.resolve('userMediator')
            // Validate request
            // const validatedData = await validator.validate(request.body, 'userUpdate');

            const result = userMediator.updateUser(userId, request.body);
            reply.send(result);


        } catch (error) {
            logger.error(`Failed to update the user ${error}`)
            throw error
        }
    },
    async deleteUser(request, reply) {
        try {

            const { userId } = request.params;
            const requesterId = request.user?.id;

            // Check authorization
            if (userId !== requesterId && request.user?.role !== 'admin') {
                throw new Error('Unauthorized to delete this user');
            }
            const userMediator = request.server.container.resolve('userMediator')
            const result = await userMediator.deleteUser(userId)
            reply.send(result);


        } catch (error) {
            logger.error(`Failed ot delete the user ${error}`)
            throw error
        }
    },

}

module.exports = userController;