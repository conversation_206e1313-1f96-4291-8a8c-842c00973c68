const fs = require('fs').promises;
const path = require('path');
const { logger } = require('../utils/logger');

/**
 * Load all dependencies in the correct order
 * @param {object} container - DI Container instance
 * @param {string} baseDir - Base directory for module loading
 * @returns {object} Container with all dependencies loaded
 */
const loadAllDependencies = async (container, masterDb, replicaDb, baseDir = __dirname) => {
  try {
    logger.info('🚀 Starting dependency loading...');

    // Load in dependency order
    await loadConfigurations(container, baseDir);
    await loadUtilities(container, baseDir);
    await loadModels(container, masterDb);
    await loadModels(container, replicaDb, {skipCache : true});
    await loadStrategies(container, baseDir);
    await loadRepositories(container, baseDir);
    await loadServices(container, baseDir);
    await loadMediators(container, baseDir);

    logger.info(`✅ All dependencies loaded successfully (${container.size()} total)`);
    return container;

  } catch (error) {
    logger.error('❌ Failed to load dependencies:', error);
    throw error;
  }
};

/**
 * Load configuration modules
 */
const loadConfigurations = async (container, baseDir) => {
  logger.info('📁 Loading configurations...');
  const configs = await loadModulesFromDirectory('config', baseDir);
  container.registerBatch(configs);
  logger.info(`✅ Loaded ${Object.keys(configs).length} configurations`);
};

/**
 * Load utility modules
 */
const loadUtilities = async (container, baseDir) => {
  logger.info('🔧 Loading utilities...');
  const utils = await loadModulesFromDirectory('utils', baseDir);
  container.registerBatch(utils);
  logger.info(`✅ Loaded ${Object.keys(utils).length} utilities`);
};

/**
 * Load and initialize models
 */
const loadModels = async (container, masterDb, options = {}) => {
  logger.info('🗃️ Loading models...');
  try {
    const { initializeModels } = require('../models');
    const models = initializeModels();
    container.register('models', models, { singleton: true });
    logger.info(`✅ Loaded and initialized models`);
  } catch (error) {
    logger.error('❌ Failed to load models:', error);
    throw error;
  }
};

/**
 * Load strategy modules
 */
const loadStrategies = async (container, baseDir) => {
  logger.info('🎯 Loading strategies...');
  const strategies = await loadFactoryModules('strategies', 'Strategy', container, baseDir);
  logger.info(`✅ Loaded ${Object.keys(strategies).length} strategies`);
};

/**
 * Load repository modules
 */
const loadRepositories = async (container, baseDir) => {
  logger.info('🏪 Loading repositories...');
  const repositories = await loadFactoryModules('repositories', 'Repository', container, baseDir);
  logger.info(`✅ Loaded ${Object.keys(repositories).length} repositories`);
};

/**
 * Load service modules
 */
const loadServices = async (container, baseDir) => {
  logger.info('⚙️ Loading services...');
  const services = await loadFactoryModules('services', 'Service', container, baseDir);
  logger.info(`✅ Loaded ${Object.keys(services).length} services`);
};

/**
 * Load mediator modules with validation
 */
const loadMediators = async (container, baseDir) => {
  logger.info('🔄 Loading mediators...');
  const mediators = await loadMediatorsWithValidation(container, baseDir);
  logger.info(`✅ Loaded ${Object.keys(mediators).length} mediators`);
};

/**
 * Load modules from a directory
 */
const loadModulesFromDirectory = async (directory, baseDir) => {
  const dirPath = path.join(baseDir, '..', directory);
  const modules = {};

  try {
    const files = await fs.readdir(dirPath);

    for (const file of files) {
      if (shouldLoadFile(file)) {
        const moduleName = getModuleName(file);
        const modulePath = path.join(dirPath, file);
        
        try {
          const module = require(modulePath);
          modules[moduleName] = module;
          logger.debug(`✓ Loaded ${directory}/${moduleName}`);
        } catch (error) {
          logger.error(`❌ Failed to load ${directory}/${moduleName}:`, error.message);
        }
      }
    }
  } catch (error) {
    logger.warn(`Directory not found or inaccessible: ${dirPath}`);
  }

  return modules;
};

/**
 * Load factory-based modules (repositories, services, strategies)
 */
const loadFactoryModules = async (directory, suffix, container, baseDir) => {
  const dirPath = path.join(baseDir, '..', directory);
  const modules = {};

  try {
    const files = await fs.readdir(dirPath);

    for (const file of files) {
      const singularDirName = dirPath.endsWith('ies') ? `${directory.slice(0, -3)}y` : directory.slice(0, -1);
      
      if (file.endsWith(`.${singularDirName}.js`)) { // Remove 's' from directory name
        const name = file.replace(`.${singularDirName}.js`, '');
        const modulePath = path.join(dirPath, file);
        
        try {
          const module = require(modulePath);
          const createFunction = module[`create${capitalize(name)}${suffix}`];
          
          if (createFunction && typeof createFunction === 'function') {
            let instance;
            if (suffix === 'Repository') {
              // For repositories, pass models specifically
              const allDeps = container.resolveAll();
              instance = createFunction({ models: allDeps.models, ...allDeps });
            } else if (suffix === 'Service') {
              // For services, pass all dependencies including repositories
              const allDeps = container.resolveAll();
              instance = createFunction(allDeps);
            } else {
              // For other factory modules, pass all dependencies
              instance = createFunction(container.resolveAll());
            }
            const instanceName = `${name}${suffix}`;
            
            modules[instanceName] = instance;
            container.register(instanceName, instance);
            
            logger.debug(`✓ Loaded ${singularDirName}: ${name}`);
          } else {
            logger.warn(`❌ No create function found for ${singularDirName}: ${name}`);
          }
        } catch (error) {
          logger.error(`❌ Failed to load ${singularDirName} ${name}: ${error.message}`);
        }
      }
    }
  } catch (error) {
    logger.warn(`Directory not found or inaccessible: ${dirPath}`);
  }

  return modules;
};

/**
 * Load mediators with validation schemas
 */
const loadMediatorsWithValidation = async (container, baseDir) => {
  const mediatorPath = path.join(baseDir, '..', 'mediators');  
  const mediators = {};

  try {
    const files = await fs.readdir(mediatorPath);

    for (const file of files) {
      if (file.endsWith('.mediator.js')) {
        const name = file.replace('.mediator.js', '');
        const modulePath = path.join(mediatorPath, file);
        
        try {
          const module = require(modulePath);
          const createFunction = module[`create${capitalize(name)}Mediator`];
          
          if (createFunction && typeof createFunction === 'function') {
            // Load schemas for validation
            const schemas = await loadSchema(name, baseDir);
            const { createValidator } = require('../utils/validator');
            const validator = createValidator(schemas);
            
            const mediator = createFunction({
              ...container.resolveAll(),
              validator
            });
            
            const validatorName = `${name}Validator`;
            const instanceName = `${name}Mediator`;
            mediators[instanceName] = mediator;
            
            container.register(instanceName, mediator);
            container.register(validatorName, validator);
            
            logger.debug(`✓ Loaded mediator: ${name}`);
          } else {
            logger.warn(`❌ No create function found for mediator: ${name}`);
          }
        } catch (error) {
          logger.error(`❌ Failed to load mediator ${name}:`, error.message);
        }
      }
    }
  } catch (error) {
    logger.warn(`Directory not found or inaccessible: ${mediatorPath}`);
  }
  
  return mediators;
};

/**
 * Load schema for validation
 */
const loadSchema = async (name, baseDir) => {
  try {
    const schemaPath = path.join(baseDir, '..', 'schemas', `${name}.schema.js`);
    return require(schemaPath);
  } catch (error) {
    logger.debug(`No schema found for ${name}`);
    return {};
  }
};

/**
 * Check if file should be loaded
 */
const shouldLoadFile = (file) => {
  return file.endsWith('.js') && 
         !file.includes('.test.') && 
         !file.includes('.spec.') &&
         !file.startsWith('.');
};

/**
 * Extract module name from filename
 */
const getModuleName = (file) => {
  return file
    .replace('.js', '')
    .replace('.config', '')
    .replace(/\.(service|repository|strategy|mediator)$/, '');
};

/**
 * Capitalize first letter
 */
const capitalize = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1);
};

/**
 * Get loader statistics
 */
const getLoaderStats = (container, baseDir) => {
  return {
    ...container.getStats(),
    baseDirectory: baseDir
  };
};

/**
 * Load dependencies with custom configuration
 */
const loadDependenciesWithConfig = async (container, options = {}) => {
  const {
    baseDir = __dirname,
    skipModules = [],
    loadOrder = [
      'configurations',
      'utilities', 
      'models',
      'strategies',
      'repositories',
      'services',
      'mediators'
    ]
  } = options;

  logger.info('🔧 Loading dependencies with custom configuration...');

  const loaderMap = {
    configurations: () => loadConfigurations(container, baseDir),
    utilities: () => loadUtilities(container, baseDir),
    models: () => loadModels(container, baseDir),
    strategies: () => loadStrategies(container, baseDir),
    repositories: () => loadRepositories(container, baseDir),
    services: () => loadServices(container, baseDir),
    mediators: () => loadMediators(container, baseDir)
  };

  for (const moduleName of loadOrder) {
    if (!skipModules.includes(moduleName) && loaderMap[moduleName]) {
      await loaderMap[moduleName]();
    } else if (skipModules.includes(moduleName)) {
      logger.info(`⏭️ Skipping ${moduleName} (configured to skip)`);
    }
  }

  return container;
};

/**
 * Load specific module type
 */
const loadSpecificModule = async (container, moduleType, baseDir = __dirname) => {
  const loaderMap = {
    config: () => loadConfigurations(container, baseDir),
    utils: () => loadUtilities(container, baseDir),
    models: () => loadModels(container, baseDir),
    strategies: () => loadStrategies(container, baseDir),
    repositories: () => loadRepositories(container, baseDir),
    services: () => loadServices(container, baseDir),
    mediators: () => loadMediators(container, baseDir)
  };

  if (!loaderMap[moduleType]) {
    throw new Error(`Unknown module type: ${moduleType}. Available: ${Object.keys(loaderMap).join(', ')}`);
  }

  return await loaderMap[moduleType]();
};

module.exports = { 
  loadAllDependencies,
  loadConfigurations,
  loadUtilities,
  loadModels,
  loadStrategies,
  loadRepositories,
  loadServices,
  loadMediators,
  loadModulesFromDirectory,
  loadFactoryModules,
  loadMediatorsWithValidation,
  loadSchema,
  loadDependenciesWithConfig,
  loadSpecificModule,
  getLoaderStats,
  shouldLoadFile,
  getModuleName,
  capitalize
};

// const fs = require('fs').promises;
// const path = require('path');
// const { logger } = require('../utils/logger');
// const { createContainer } = require('./container');

// const loadDependencies = async () => {
//   const container = createContainer();
  
//   try {
//     logger.info('Loading dependencies...');

//     // Load configurations
//     const configs = await loadModules('config', container);
    
//     // Load utilities
//     const utils = await loadModules('utils', container);
    
//     // Load strategies
//     const strategies = await loadStrategies(container);
    
//     // Load models
//     const models = require('../models');
//     container.register('models', models);
    
//     // Load repositories
//     const repositories = await loadRepositories(container);
    
//     // Load services
//     const services = await loadServices(container);
    
//     // Load mediators
//     const mediators = await loadMediators(container);

//     logger.info('✅ All dependencies loaded successfully');
    
//     return container;

//   } catch (error) {
//     logger.error('Failed to load dependencies:', error);
//     throw error;
//   }
// };

// const loadModules = async (directory, container) => {
//   const dirPath = path.join(__dirname, '..', directory);
//   const files = await fs.readdir(dirPath);
//   const modules = {};

//   for (const file of files) {
//     if (file.endsWith('.js') && !file.includes('.test.')) {
//       const moduleName = file.replace('.js', '').replace('.config', '');
//       const modulePath = path.join(dirPath, file);
      
//       try {
//         const module = require(modulePath);
//         modules[moduleName] = module;
        
//         logger.debug(`Loaded ${directory}/${moduleName}`);
//       } catch (error) {
//         logger.error(`Failed to load ${directory}/${moduleName}:`, error);
//       }
//     }
//   }

//   return modules;
// };

// const loadRepositories = async (container) => {
//   const repositories = {};
//   const repoPath = path.join(__dirname, '..', 'repositories');
//   const files = await fs.readdir(repoPath);

//   for (const file of files) {
//     if (file.endsWith('.repository.js')) {
//       const name = file.replace('.repository.js', '');
//       const modulePath = path.join(repoPath, file);
      
//       try {
//         const module = require(modulePath);
//         const createFunction = module[`create${capitalize(name)}Repository`];
        
//         if (createFunction) {
//           const repository = createFunction(container.resolve());
//           repositories[`${name}Repository`] = repository;
//           container.register(`${name}Repository`, repository);
          
//           logger.debug(`Loaded repository: ${name}`);
//         }
//       } catch (error) {
//         logger.error(`Failed to load repository ${name}:`, error);
//       }
//     }
//   }

//   return repositories;
// };

// const loadServices = async (container) => {
//   const services = {};
//   const servicePath = path.join(__dirname, '..', 'services');
//   const files = await fs.readdir(servicePath);

//   for (const file of files) {
//     if (file.endsWith('.service.js')) {
//       const name = file.replace('.service.js', '');
//       const modulePath = path.join(servicePath, file);
      
//       try {
//         const module = require(modulePath);
//         const createFunction = module[`create${capitalize(name)}Service`];
        
//         if (createFunction) {
//           const service = createFunction(container.resolve());
//           services[`${name}Service`] = service;
//           container.register(`${name}Service`, service);
          
//           logger.debug(`Loaded service: ${name}`);
//         }
//       } catch (error) {
//         logger.error(`Failed to load service ${name}:`, error);
//       }
//     }
//   }

//   return services;
// };

// const loadMediators = async (container) => {
//   const mediators = {};
//   const mediatorPath = path.join(__dirname, '..', 'mediators');
//   const files = await fs.readdir(mediatorPath);

//   for (const file of files) {
//     if (file.endsWith('.mediator.js')) {
//       const name = file.replace('.mediator.js', '');
//       const modulePath = path.join(mediatorPath, file);
      
//       try {
//         const module = require(modulePath);
//         const createFunction = module[`create${capitalize(name)}Mediator`];
        
//         if (createFunction) {
//           // Load schemas for validation
//           const schemas = await loadSchema(name);
//           const { createValidator } = require('../utils/validator');
//           const validator = createValidator(schemas);
          
//           const mediator = createFunction({
//             ...container.resolve(),
//             validator
//           });
          
//           mediators[`${name}Mediator`] = mediator;
//           container.register(`${name}Mediator`, mediator);
          
//           logger.debug(`Loaded mediator: ${name}`);
//         }
//       } catch (error) {
//         logger.error(`Failed to load mediator ${name}:`, error);
//       }
//     }
//   }

//   return mediators;
// };

// const loadStrategies = async (container) => {
//   const strategies = {};
//   const strategyPath = path.join(__dirname, '..', 'strategies');
//   const files = await fs.readdir(strategyPath);

//   for (const file of files) {
//     if (file.endsWith('.strategy.js')) {
//       const name = file.replace('.strategy.js', '');
//       const modulePath = path.join(strategyPath, file);
      
//       try {
//         const module = require(modulePath);
//         const createFunction = module[`create${capitalize(name)}Strategy`];
        
//         if (createFunction) {
//           const strategy = createFunction(container.resolve());
//           strategies[`${name}Strategy`] = strategy;
//           container.register(`${name}Strategy`, strategy);
          
//           logger.debug(`Loaded strategy: ${name}`);
//         }
//       } catch (error) {
//         logger.error(`Failed to load strategy ${name}:`, error);
//       }
//     }
//   }

//   return strategies;
// };

// const loadSchema = async (name) => {
//   try {
//     const schemaPath = path.join(__dirname, '..', 'schemas', `${name}.schema.js`);
//     return require(schemaPath);
//   } catch (error) {
//     logger.warn(`No schema found for ${name}`);
//     return {};
//   }
// };

// const capitalize = (str) => {
//   return str.charAt(0).toUpperCase() + str.slice(1);
// };

// module.exports = { loadDependencies };