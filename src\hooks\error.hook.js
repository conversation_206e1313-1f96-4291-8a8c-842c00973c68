const { logger, logError } = require('../utils/logger');
const { HTTP_STATUS, ERROR_MESSAGES } = require('../utils/constants');

const errorHook = async (request, reply, error) => {
  // Log the error with context
  logError(error, {
    requestId: request.id,
    method: request.method,
    url: request.url,
    userId: request.user?.id,
    ip: request.ip
  });

  // Handle specific error types
  if (error.validation) {
    return reply.code(HTTP_STATUS.BAD_REQUEST).send({
      error: 'Validation Error',
      message: error.message,
      details: error.validation
    });
  }

  if (error.name === 'SequelizeValidationError') {
    return reply.code(HTTP_STATUS.BAD_REQUEST).send({
      error: 'Validation Error',
      message: 'Database validation failed',
      details: error.errors.map(e => ({
        field: e.path,
        message: e.message
      }))
    });
  }

  if (error.name === 'SequelizeUniqueConstraintError') {
    return reply.code(HTTP_STATUS.CONFLICT).send({
      error: 'Conflict',
      message: 'Duplicate entry found',
      details: error.errors.map(e => ({
        field: e.path,
        message: e.message
      }))
    });
  }

  if (error.name === 'SequelizeForeignKeyConstraintError') {
    return reply.code(HTTP_STATUS.BAD_REQUEST).send({
      error: 'Constraint Error',
      message: 'Foreign key constraint failed'
    });
  }

  if (error.statusCode) {
    return reply.code(error.statusCode).send({
      error: error.name || 'Error',
      message: error.message,
      details: error.details
    });
  }

  // Default to internal server error
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  reply.code(HTTP_STATUS.INTERNAL_SERVER_ERROR).send({
    error: ERROR_MESSAGES.INTERNAL_ERROR,
    message: isDevelopment ? error.message : 'An unexpected error occurred',
    ...(isDevelopment && { stack: error.stack })
  });
};

// Not found handler
const notFoundHandler = (request, reply) => {
  reply.code(HTTP_STATUS.NOT_FOUND).send({
    error: 'Not Found',
    message: `Route ${request.method} ${request.url} not found`,
    statusCode: HTTP_STATUS.NOT_FOUND
  });
};

// Method not allowed handler
const methodNotAllowedHandler = (request, reply) => {
  reply.code(405).send({
    error: 'Method Not Allowed',
    message: `Method ${request.method} not allowed for ${request.url}`,
    statusCode: 405
  });
};

module.exports = {
  errorHook,
  notFoundHandler,
  methodNotAllowedHandler
};