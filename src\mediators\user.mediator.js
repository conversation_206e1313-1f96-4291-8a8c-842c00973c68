const { logger } = require('../utils/logger');

const createUserMediator = ({ userService, validator }) => {
  return {
    async createUser(validatedData) {
      try {
        // Validate request

        // Call service
        const user = await userService.createUser(validatedData);

        // Format response
        return {
          success: true,
          data: user,
          message: 'User created successfully'
        };

      } catch (error) {
        logger.error(`User creation failed: ${error}`);
        throw error;
      }
    },

    async getUserByEmail(email){
      try {
        const user = await userService.getUserByEmail(email);

        // Format response
        return {
          success: true,
          data: user
        };
      } catch (error) {
        logger.error(`Get user failed: ${error}`);
        throw error;
      }
    },

    async getUser(userId) {
      try {

        // Call service
        const user = await userService.getUserById(userId);

        // Format response
        return {
          success: true,
          data: user
        };

      } catch (error) {
        logger.error(`Get user failed: ${error}`);
        throw error;
      }
    },

    async updateUser(userId, validatedData) {
      try {

        // Call service
        const user = await userService.updateUser(userId, validatedData);

        // Format response
        return {
          success: true,
          user
        };

      } catch (error) {
        logger.error(`User update failed:, ${error}`);
        throw error;
      }
    },

    async deleteUser(userId) {
      try {

        // Call service
        await userService.deleteUser(userId);

        // Format response
        return {
          success: true,
          message: 'User deleted successfully'
        };

      } catch (error) {
        logger.error(`User deletion failed: ${error}`);
        throw error;
      }
    },

    async listUsers(metaData) {
      try {
        const { page, limit, search, filters } = metaData;

        // Call service
        let result;
        if (search) {
          result = await userService.searchUsers(search, { page, limit });
        } else {
          result = await userService.listUsers(filters, { page, limit });
        }

        // Format response
        return {
          success: true,
          data: result.users,
          pagination: {
            page: result.page,
            limit,
            total: result.total,
            totalPages: result.totalPages
          }
        };

      } catch (error) {
        logger.error(`List users failed: ${error}`);
        throw error;
      }
    },

    async verifyEmail(request) {
      try {
        const { token } = request.query;
        const userId = request.user?.id;

        if (!token) {
          throw new Error('Verification token is required');
        }

        // Call service
        const user = await userService.verifyEmail(userId, token);

        // Format response
        return {
          success: true,
          data: user,
          message: 'Email verified successfully'
        };

      } catch (error) {
        logger.error(`Email verification failed: ${error}`);
        throw error;
      }
    },

    async requestPasswordReset(request) {
      try {
        const { email } = request.body;

        // Validate email
        if (!validator.isEmail(email)) {
          throw new Error('Invalid email format');
        }

        // Call service
        await userService.requestPasswordReset(email);

        // Format response (don't reveal if email exists)
        return {
          success: true,
          message: 'If the email exists, a reset link has been sent'
        };

      } catch (error) {
        logger.error(`Password reset request failed: ${error}`);
        throw error;
      }
    },

    async resetPassword(request) {
      try {
        const { token, newPassword } = request.body;
        const userId = request.user?.id;

        // Validate password
        const validatedPassword = await validator.validate(
          { password: newPassword },
          'passwordReset'
        );

        // Call service
        await userService.resetPassword(userId, token, validatedPassword.password);

        // Format response
        return {
          success: true,
          message: 'Password reset successfully'
        };

      } catch (error) {
        logger.error(`Password reset failed: ${error}`);
        throw error;
      }
    },

    async changePassword(request) {
      try {
        const { currentPassword, newPassword } = request.body;
        const userId = request.user?.id;

        // Validate passwords
        const validatedData = await validator.validate(request.body, 'passwordChange');

        // Call service
        await userService.changePassword(
          userId,
          validatedData.currentPassword,
          validatedData.newPassword
        );

        // Format response
        return {
          success: true,
          message: 'Password changed successfully'
        };

      } catch (error) {
        logger.error(`Password change failed: ${error}`);
        throw error;
      }
    },

    async getUserProfile(request) {
      try {
        const userId = request.user?.id;

        // Call service
        if (userId) {
          const user = await userService.getUserById(userId);
          // Format response with additional profile data
          return {
            success: true,
            data: {
              ...user.toJSON(),
              completionPercentage: this.calculateProfileCompletion(user)
            }
          };
        }
        else throw new Error('Request not associated with any user, missing user id')

      } catch (error) {
        logger.error(`Get user profile failed: ${error}`);
        throw error;
      }
    },

    async updateUserPreferences(request) {
      try {
        const userId = request.user?.id;
        const { preferences } = request.body;

        // Validate preferences
        const validatedPreferences = await validator.validate(
          { preferences },
          'userPreferences'
        );

        // Call service
        const updatedPreferences = await userService.updateUserPreferences(
          userId,
          validatedPreferences.preferences
        );

        // Format response
        return {
          success: true,
          data: updatedPreferences,
          message: 'Preferences updated successfully'
        };

      } catch (error) {
        logger.error(`Update preferences failed: ${error}`);
        throw error;
      }
    },

    async getUserStatistics(request) {
      try {
        // Check admin authorization
        if (request.user?.role !== 'admin') {
          throw new Error('Unauthorized access');
        }

        // Call service
        const stats = await userService.getUserStatistics();

        // Format response
        return {
          success: true,
          data: stats
        };

      } catch (error) {
        logger.error(`Get user statistics failed: ${error}`);
        throw error;
      }
    },

    async bulkCreateUsers(request) {
      try {
        // Check admin authorization
        if (request.user?.role !== 'admin') {
          throw new Error('Unauthorized access');
        }

        const { users } = request.body;

        // Validate bulk data
        const validatedUsers = await Promise.all(
          users.map(user => validator.validate(user, 'userCreate'))
        );

        // Call service
        const createdUsers = await userService.bulkCreateUsers(validatedUsers);

        // Format response
        return {
          success: true,
          data: createdUsers,
          message: `Successfully created ${createdUsers.length} users`
        };

      } catch (error) {
        logger.error(`Bulk user creation failed: ${error}`);
        throw error;
      }
    },

    async exportUsers(request) {
      try {
        // Check admin authorization
        if (request.user?.role !== 'admin') {
          throw new Error('Unauthorized access');
        }

        const { format = 'json', filters = {} } = request.query;

        // Call service
        const users = await userService.listUsers(filters, { limit: 10000 });

        // Format data based on requested format
        let exportData;
        switch (format) {
          case 'csv':
            exportData = this.convertToCSV(users.users);
            break;
          case 'json':
          default:
            exportData = users.users;
        }

        // Format response
        return {
          success: true,
          data: exportData,
          format,
          count: users.users.length
        };

      } catch (error) {
        logger.error('User export failed:', error);
        throw error;
      }
    },

    // Helper methods
    calculateProfileCompletion(user) {
      const requiredFields = [
        'email',
        'firstName',
        'lastName',
        'emailVerified'
      ];

      const optionalFields = [
        'phone',
        'address',
        'dateOfBirth',
        'preferences'
      ];

      let completed = 0;
      let total = requiredFields.length + optionalFields.length;

      // Check required fields
      requiredFields.forEach(field => {
        if (user[field]) completed++;
      });

      // Check optional fields
      optionalFields.forEach(field => {
        if (user[field] && (typeof user[field] !== 'object' || Object.keys(user[field]).length > 0)) {
          completed++;
        }
      });

      return Math.round((completed / total) * 100);
    },

    convertToCSV(users) {
      if (users.length === 0) return '';

      const headers = ['id', 'email', 'username', 'firstName', 'lastName', 'role', 'status', 'createdAt'];
      const rows = users.map(user =>
        headers.map(header => user[header] || '').join(',')
      );

      return [headers.join(','), ...rows].join('\n');
    }
  };
};

module.exports = { createUserMediator };