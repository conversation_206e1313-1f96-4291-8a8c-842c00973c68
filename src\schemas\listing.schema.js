const Joi = require('joi');

// Common schemas
const uuidSchema = Joi.string().uuid().required();
const optionalUuidSchema = Joi.string().uuid().optional();
const paginationSchema = {
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20)
};

// Listing schemas
const createListingSchema = Joi.object({
  CategoryID: uuidSchema,
  SubcategoryID: optionalUuidSchema,
  Title: Joi.string().min(3).max(200).required(),
  Description: Joi.string().min(10).max(5000).required(),
  Location: Joi.string().min(3).max(200).required(),
  Latitude: Joi.number().min(-90).max(90).optional(),
  Longitude: Joi.number().min(-180).max(180).optional(),
  PricePerDay: Joi.number().positive().precision(2).required(),
  PricePerWeek: Joi.number().positive().precision(2).optional(),
  PricePerMonth: Joi.number().positive().precision(2).optional(),
  SecurityDeposit: Joi.number().min(0).precision(2).default(0),
  Condition: Joi.string().valid('new', 'good', 'used').default('good'),
  DeliveryOptions: Joi.string().valid('pickup', 'homeDelivery').default('pickup'),
  IsAvailable: Joi.boolean().default(true),
  IsActive: Joi.boolean().default(true)
});

const updateListingSchema = Joi.object({
  CategoryID: optionalUuidSchema,
  SubcategoryID: optionalUuidSchema,
  Title: Joi.string().min(3).max(200).optional(),
  Description: Joi.string().min(10).max(5000).optional(),
  Location: Joi.string().min(3).max(200).optional(),
  Latitude: Joi.number().min(-90).max(90).optional(),
  Longitude: Joi.number().min(-180).max(180).optional(),
  PricePerDay: Joi.number().positive().precision(2).optional(),
  PricePerWeek: Joi.number().positive().precision(2).optional(),
  PricePerMonth: Joi.number().positive().precision(2).optional(),
  SecurityDeposit: Joi.number().min(0).precision(2).optional(),
   Condition: Joi.string().valid('new', 'good', 'used').default('good'),
  DeliveryOptions: Joi.string().valid('pickup', 'homeDelivery').default('pickup'),
  IsAvailable: Joi.boolean().optional(),
  IsActive: Joi.boolean().optional()
});

const getListingsSchema = Joi.object({
  ...paginationSchema,
  categoryId: optionalUuidSchema,
  subcategoryId: optionalUuidSchema,
  location: Joi.string().min(2).max(100).optional(),
  minPrice: Joi.number().positive().optional(),
  maxPrice: Joi.number().positive().optional(),
  condition: Joi.string().valid('new', 'good', 'used').optional(),
  isAvailable: Joi.boolean().default(true),
  isActive: Joi.boolean().default(true),
  sortBy: Joi.string().valid(
    'CreatedAt', 'PricePerDay', 'Title', 'ViewCount', 'FeaturedUntil'
  ).default('CreatedAt'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC'),
  search: Joi.string().min(2).max(100).optional(),
  featured: Joi.boolean().optional()
});

const searchListingsSchema = Joi.object({
  ...paginationSchema,
  search: Joi.string().min(2).max(100).optional(),
  categoryId: optionalUuidSchema,
  subcategoryId: optionalUuidSchema,
  location: Joi.string().min(2).max(100).optional(),
  minPrice: Joi.number().positive().optional(),
  maxPrice: Joi.number().positive().optional(),
  condition: Joi.string().valid('new', 'good', 'used').optional(),
  sortBy: Joi.string().valid(
    'CreatedAt', 'PricePerDay', 'Title', 'ViewCount', 'relevance'
  ).default('CreatedAt'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC')
});

const getListingByIdSchema = Joi.object({
  id: uuidSchema,
  incrementView: Joi.boolean().default(false)
});

const getRelatedListingsSchema = Joi.object({
  id: uuidSchema,
  limit: Joi.number().integer().min(1).max(20).default(6)
});

const getUserListingsSchema = Joi.object({
  ...paginationSchema,
  userId: uuidSchema
});

// Category schemas
const createCategorySchema = Joi.object({
  Name: Joi.string().min(2).max(100).required(),
  Description: Joi.string().max(500).optional(),
  IconURL: Joi.string().uri().optional()
});

const updateCategorySchema = Joi.object({
  Name: Joi.string().min(2).max(100).optional(),
  Description: Joi.string().max(500).optional(),
  IconURL: Joi.string().uri().optional(),
  IsActive: Joi.boolean().optional()
});

const createSubcategorySchema = Joi.object({
  CategoryID: uuidSchema,
  Name: Joi.string().min(2).max(100).required(),
  Description: Joi.string().max(500).optional(),
  IconURL: Joi.string().uri().optional()
});

const updateSubcategorySchema = Joi.object({
  Name: Joi.string().min(2).max(100).optional(),
  Description: Joi.string().max(500).optional(),
  IconURL: Joi.string().uri().optional(),
  IsActive: Joi.boolean().optional()
});

// Parameter schemas
const listingIdParamSchema = Joi.object({
  id: uuidSchema
});

const categoryIdParamSchema = Joi.object({
  id: uuidSchema
});

const subcategoryIdParamSchema = Joi.object({
  id: uuidSchema
});

module.exports = {
  // Listing schemas
  createListingSchema,
  updateListingSchema,
  getListingsSchema,
  searchListingsSchema,
  getListingByIdSchema,
  getRelatedListingsSchema,
  getUserListingsSchema,
  
  // Category schemas
  createCategorySchema,
  updateCategorySchema,
  createSubcategorySchema,
  updateSubcategorySchema,
  
  // Parameter schemas
  listingIdParamSchema,
  categoryIdParamSchema,
  subcategoryIdParamSchema
};
