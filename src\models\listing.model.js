const { DataTypes } = require('sequelize');

const defineListingModel = (sequelize) => {
  const Listing = sequelize.define('Listing', {
    ListingID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    ListerID: {
      type: DataTypes.UUID,
      allowNull: false,
      defaultValue: 'test-user-id'
    },
    CategoryID: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: 'categories',
        key: 'CategoryID'
      }
    },
    SubcategoryID: {
      type: DataTypes.UUID,
      allowNull: true,
      references: {
        model: 'subcategories',
        key: 'SubcategoryID'
      }
    },
    Title: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    Description: {
      type: DataTypes.TEXT,
      allowNull: false
    },
    Location: {
      type: DataTypes.STRING(200),
      allowNull: false
    },
    Latitude: {
      type: DataTypes.DECIMAL(10, 8),
      allowNull: true
    },
    Longitude: {
      type: DataTypes.DECIMAL(11, 8),
      allowNull: true
    },
    PricePerDay: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    PricePerWeek: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    PricePerMonth: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true
    },
    SecurityDeposit: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      defaultValue: 0
    },
    Condition: {
      type: DataTypes.ENUM('new', 'good', 'used'),
      allowNull: false,
      defaultValue: 'new',
    },
    DeliveryOptions: {
      type: DataTypes.ENUM('pickup', 'homeDelivery'),
      allowNull: false,
      defaultValue: 'pickup',
    },
    StartDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    EndDate: {
      type: DataTypes.DATE,
      allowNull: true
    },
    IsAvailable: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    IsActive: {
      type: DataTypes.BOOLEAN,
      defaultValue: true
    },
    ViewCount: {
      type: DataTypes.INTEGER,
      defaultValue: 0
    },
    FeaturedUntil: {
      type: DataTypes.DATE,
      allowNull: true
    },
    CreatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'listings',
    timestamps: false,
    indexes: [
      { fields: ['CategoryID'] },
      { fields: ['SubcategoryID'] },
      { fields: ['ListerID'] },
      { fields: ['Location'] },
      { fields: ['PricePerDay'] },
      { fields: ['IsAvailable'] },
      { fields: ['IsActive'] },
      { fields: ['CreatedAt'] },
      { fields: ['FeaturedUntil'] }
    ],
    hooks: {
      beforeUpdate: (listing) => {
        listing.UpdatedAt = new Date();
      }
    }
  });

  Listing.associate = (models) => {
    // Temporarily commented out User association
    // Listing.belongsTo(models.User, {
    //   foreignKey: 'ListerID',
    //   as: 'Lister'
    // });

    Listing.belongsTo(models.Category, {
      foreignKey: 'CategoryID',
      as: 'Category'
    });

    Listing.belongsTo(models.Subcategory, {
      foreignKey: 'SubcategoryID',
      as: 'Subcategory'
    });
  };

  return Listing;
};

module.exports = defineListingModel;
