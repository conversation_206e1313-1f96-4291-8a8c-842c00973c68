# Database Setup Guide

This guide will help you set up the database for the Rentle Backend API.

## Prerequisites

1. **PostgreSQL** installed and running
2. **Node.js** and **npm** installed
3. **Environment variables** configured

## Quick Setup

### 1. Configure Environment Variables

Copy the example environment file and configure your database settings:

```bash
cp .env.example .env
```

Edit the `.env` file with your database credentials:

```env
# Database Configuration
DB_MASTER_HOST=localhost
DB_MASTER_PORT=5432
DB_MASTER_USERNAME=postgres
DB_MASTER_PASSWORD=your_password
DB_MASTER_DATABASE=rentle_db

# For development, use same database for replica
DB_REPLICA_HOST=localhost
DB_REPLICA_PORT=5432
DB_REPLICA_USERNAME=postgres
DB_REPLICA_PASSWORD=your_password
DB_REPLICA_DATABASE=rentle_db

# Force database sync (creates tables automatically)
FORCE_DB_SYNC=true
```

### 2. Create Database

Create the database in PostgreSQL:

```sql
CREATE DATABASE rentle_db;
```

Or using psql command line:

```bash
psql -U postgres -c "CREATE DATABASE rentle_db;"
```

### 3. Setup Database Tables

Run the database setup script to create all tables:

```bash
npm run db:setup
```

This will:
- Connect to the database
- Initialize all models
- Create all tables automatically
- Show you which tables were created

### 4. Verify Setup

Start the server to verify everything is working:

```bash
npm run dev
```

You should see logs indicating successful database connection and model synchronization.

## Alternative Setup Methods

### Method 1: Force Sync (Recreates Tables)

If you want to completely recreate all tables (⚠️ **This will delete all data**):

```bash
npm run db:sync
```

### Method 2: Manual Setup

If you prefer manual control:

1. Set `FORCE_DB_SYNC=false` in your `.env` file
2. Start the server normally: `npm run dev`
3. Tables will be created automatically on first run

## Database Tables

The following tables will be created:

- **users** - User accounts and profiles
- **categories** - Product categories
- **subcategories** - Product subcategories
- **listings** - Rental listings with all details

## Troubleshooting

### Connection Issues

If you get connection errors:

1. **Check PostgreSQL is running:**
   ```bash
   # On Windows
   net start postgresql-x64-14
   
   # On macOS
   brew services start postgresql
   
   # On Linux
   sudo systemctl start postgresql
   ```

2. **Verify database credentials** in your `.env` file

3. **Check if database exists:**
   ```bash
   psql -U postgres -l
   ```

### Permission Issues

If you get permission errors:

1. **Grant privileges to your user:**
   ```sql
   GRANT ALL PRIVILEGES ON DATABASE rentle_db TO your_username;
   ```

2. **Or create a new user:**
   ```sql
   CREATE USER rentle_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE rentle_db TO rentle_user;
   ```

### Table Creation Issues

If tables are not being created:

1. **Check the logs** for any error messages
2. **Verify NODE_ENV** is set to `development`
3. **Set FORCE_DB_SYNC=true** in your `.env` file
4. **Run the setup script again:** `npm run db:setup`

## Environment Variables Reference

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_MASTER_HOST` | Database host for write operations | localhost |
| `DB_MASTER_PORT` | Database port | 5432 |
| `DB_MASTER_USERNAME` | Database username | postgres |
| `DB_MASTER_PASSWORD` | Database password | - |
| `DB_MASTER_DATABASE` | Database name | rentle_db |
| `FORCE_DB_SYNC` | Force table creation/update | false |
| `NODE_ENV` | Environment mode | development |

## Production Considerations

For production deployment:

1. **Set `FORCE_DB_SYNC=false`** to prevent accidental table recreation
2. **Use separate read/write databases** for better performance
3. **Set up proper database backups**
4. **Use environment-specific credentials**
5. **Enable SSL connections** for security

## Need Help?

If you encounter any issues:

1. Check the application logs for detailed error messages
2. Verify your PostgreSQL installation and configuration
3. Ensure all environment variables are correctly set
4. Try running the setup script with debug logging: `LOG_LEVEL=debug npm run db:setup`
