const { logger } = require('../utils/logger');

const createAuthMediator = ({ authService, validator }) => {
  return {
    async login(validatedData, jwt) {
      try {
        const { user, refreshToken } = await authService.login(validatedData);
    
        const accessToken = await jwt.sign({
          id: user.id,
          email: user.email,
          role: user.role
        });
  
    
        return {
          success: true,
          data: {
            user: user.toJSON(),
            accessToken,
            refreshToken,
            expiresIn: 604800
          }
        };
      } catch (error) {
        logger.error('Auth login failed:', error);
        throw error;
      }
    },

    async refreshToken(data, request) {
      try {
        const result = await authService.refreshToken(data, request);
        return {
          success: true,
          data: result
        };
      } catch (error) {
        logger.error('Auth token refresh failed:', error);
        throw error;
      }
    },

    async logout(user) {
      try {
        const result = await authService.logout(user);
        return {
          success: true,
          message: result
        };
      } catch (error) {
        logger.error('Auth logout failed:', error);
        throw error;
      }
    },

    async resetPassword(data) {
      try {
        const result = await authService.resetPassword(data);
        return {
          success: true,
          message: result
        };
      } catch (error) {
        logger.error('Auth reset password failed:', error);
        throw error;
      }
    },

    async requestPasswordReset(data) {
      try {
        if (!validator.isEmail(data.email)) {
          throw new Error('Invalid email format');
        }

        const result = await authService.requestPasswordReset(data.email);
        return {
          success: true,
          message: result
        };
      } catch (error) {
        logger.error('Auth request password reset failed:', error);
        throw error;
      }
    }
  };
};

module.exports = { createAuthMediator };
