const { requireRole } = require('../../../hooks/auth.hook');
const userController = require('../controllers/user.controller')
const usersRoutes = async (fastify, options) => {
  // Get current user profile
  fastify.get('/profile', {
    schema: {
      description: 'Get current user profile',
      tags: ['users'],
    //  security: [{ bearerAuth: [] }],
      response: {
        200: {
          description: 'User profile',
          type: 'object',
          properties: {
            success: { type: 'boolean' },
            data: {
              type: 'object',
              properties: {
                id: { type: 'string' },
                email: { type: 'string' },
                username: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                role: { type: 'string' },
                status: { type: 'string' },
                emailVerified: { type: 'boolean' },
                createdAt: { type: 'string' },
                completionPercentage: { type: 'number' }
              }
            }
          }
        }
      }
    }
  }, userController.getUser);

  // Update current user profile
  fastify.put('/profile/:userId', {
    schema: {
      description: 'Update current user profile',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        properties: {
          firstName: { type: 'string' },
          lastName: { type: 'string' },
          phone: { type: 'string' },
          dateOfBirth: { type: 'string', format: 'date' },
          address: {
            type: 'object',
            properties: {
              street: { type: 'string' },
              city: { type: 'string' },
              state: { type: 'string' },
              country: { type: 'string' },
              postalCode: { type: 'string' }
            }
          }
        }
      }
    }
  }, userController.updateUser);

  // Change password
  fastify.post('/change-password', {
    schema: {
      description: 'Change user password',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['currentPassword', 'newPassword'],
        properties: {
          currentPassword: { type: 'string' },
          newPassword: { type: 'string', minLength: 8 }
        }
      }
    }
  }, async (request, reply) => {
    const { userMediator } = fastify.container.resolve('userMediator');
    const result = await userMediator.changePassword(request);
    reply.send(result);
  });

  // Update preferences
  fastify.put('/preferences', {
    schema: {
      description: 'Update user preferences',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        properties: {
          preferences: {
            type: 'object',
            properties: {
              notifications: {
                type: 'object',
                properties: {
                  email: { type: 'boolean' },
                  sms: { type: 'boolean' },
                  push: { type: 'boolean' }
                }
              },
              language: { type: 'string' },
              timezone: { type: 'string' },
              theme: { type: 'string', enum: ['light', 'dark', 'auto'] }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { userMediator } = fastify.container.resolve('userMediator');
    const result = await userMediator.updateUserPreferences(request);
    reply.send(result);
  });

  // Verify email
  fastify.get('/verify-email', {
    schema: {
      description: 'Verify user email',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        required: ['token'],
        properties: {
          token: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    const { userMediator } = fastify.container.resolve('userMediator');
    const result = await userMediator.verifyEmail(request);
    reply.send(result);
  });

  // Admin routes

  // List all users (admin only)
  fastify.get('/', {
    preHandler: [requireRole('admin')],
    schema: {
      description: 'List all users',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          page: { type: 'integer', minimum: 1, default: 1 },
          limit: { type: 'integer', minimum: 1, maximum: 100, default: 20 },
          status: { type: 'string', enum: ['active', 'inactive', 'suspended'] },
          role: { type: 'string', enum: ['user', 'admin', 'moderator'] },
          search: { type: 'string' }
        }
      }
    }
  }, userController.listUsers);

  // Get user by ID (admin only)
  fastify.get('/:userId', {
    preHandler: [requireRole('admin')],
    schema: {
      description: 'Get user by ID',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'string', format: 'uuid' }
        }
      }
    }
  }, userController.getUser);

  // Update user (admin only)
  fastify.put('/:userId', {
    preHandler: [requireRole('admin')],
    schema: {
      description: 'Update user',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'string', format: 'uuid' }
        }
      }
    }
  }, userController.updateUser);

  // Delete user (admin only)
  fastify.delete('/:userId', {
    preHandler: [requireRole('admin')],
    schema: {
      description: 'Delete user',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      params: {
        type: 'object',
        required: ['userId'],
        properties: {
          userId: { type: 'string', format: 'uuid' }
        }
      }
    }
  }, userController.deleteUser);

  // Get user statistics (admin only)
  fastify.get('/statistics/overview', {
    preHandler: [requireRole('admin')],
    schema: {
      description: 'Get user statistics',
      tags: ['users'],
      security: [{ bearerAuth: [] }]
    }
  }, async (request, reply) => {
    const { userMediator } = fastify.container.resolve('userMediator');
    const result = await userMediator.getUserStatistics(request);
    reply.send(result);
  });

  // Bulk create users (admin only)
  fastify.post('/bulk', {
    preHandler: [requireRole('admin')],
    schema: {
      description: 'Bulk create users',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      body: {
        type: 'object',
        required: ['users'],
        properties: {
          users: {
            type: 'array',
            items: {
              type: 'object',
              required: ['email', 'password', 'firstName', 'lastName'],
              properties: {
                email: { type: 'string', format: 'email' },
                username: { type: 'string' },
                password: { type: 'string' },
                firstName: { type: 'string' },
                lastName: { type: 'string' },
                role: { type: 'string', enum: ['user', 'admin', 'moderator'] }
              }
            }
          }
        }
      }
    }
  }, async (request, reply) => {
    const { userMediator } = fastify.container.resolve('userMediator');
    const result = await userMediator.bulkCreateUsers(request);
    reply.code(201).send(result);
  });

  // Export users (admin only)
  fastify.get('/export/data', {
    preHandler: [requireRole('admin')],
    schema: {
      description: 'Export users data',
      tags: ['users'],
      security: [{ bearerAuth: [] }],
      querystring: {
        type: 'object',
        properties: {
          format: { type: 'string', enum: ['json', 'csv'], default: 'json' },
          status: { type: 'string' },
          role: { type: 'string' }
        }
      }
    }
  }, async (request, reply) => {
    const { userMediator } = fastify.container.resolve('userMediator');
    const result = await userMediator.exportUsers(request);

    if (request.query.format === 'csv') {
      reply
        .header('Content-Type', 'text/csv')
        .header('Content-Disposition', 'attachment; filename="users.csv"')
        .send(result.data);
    } else {
      reply.send(result);
    }
  });
};

module.exports = usersRoutes;