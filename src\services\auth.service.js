const { v4: uuidv4 } = require('uuid');
const bcrypt = require('bcrypt');
const { logger } = require('../utils/logger');
const { publishToRabbit } = require('../config/queue.config');

const createAuthService = ({ userRepository, cache }) => {
  return {
    async login(validatedData) {
        const { email, password } = validatedData;
        const user = await userRepository.findByEmail(email);
        if (!user) throw new Error('Invalid credentials');
      
        if (user.isLocked()) throw new Error('Account is locked. Please try again later');
      
        const isValid = await user.comparePassword(password);
        if (!isValid) {
          await user.incrementLoginAttempts();
          throw new Error('Invalid credentials');
        }
      
        if (user.status !== 'active') throw new Error('Account is not active');
      
        await user.resetLoginAttempts();
        await userRepository.updateLastLogin(user.id);
      
        const refreshToken = uuidv4();
        await cache.cache.set(`refresh_token:${refreshToken}`, { userId: user.id }, 30 * 24 * 60 * 60);
      
        await publishToRabbit('events', 'user.login', {
          userId: user.id,
          timestamp: new Date(),
        });
      
        return {
          user,
          refreshToken
        };
      },

    async refreshToken(refreshToken) {
      const tokenData = await cache.get(`refresh_token:${refreshToken}`);
      if (!tokenData) throw new Error('Invalid refresh token');

      const user = await userRepository.findById(tokenData.userId);
      if (!user || user.status !== 'active') throw new Error('User not found or inactive');

      const newAccessToken = await jwt.sign({
        id: user.id,
        email: user.email,
        role: user.role
      });

      const newRefreshToken = uuidv4();
      await cache.delete(`refresh_token:${refreshToken}`);
      await cache.set(`refresh_token:${newRefreshToken}`, { userId: user.id }, 30 * 24 * 60 * 60);

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
        expiresIn: 604800
      };
    },

    async logout(userId) {
      await publishToRabbit('events', 'user.logout', {
        userId,
        timestamp: new Date()
      });
      return true;
    },

    async resetPassword(token, newPassword) {
      const users = await userRepository.findAll({ passwordResetToken: token });
      if (users.length === 0) throw new Error('Invalid or expired reset token');

      const user = users[0];
      if (user.passwordResetExpires < new Date()) {
        throw new Error('Reset token has expired');
      }

      await userRepository.update(user.id, {
        password: newPassword,
        passwordResetToken: null,
        passwordResetExpires: null
      });

      await publishToRabbit('events', 'user.password-reset', {
        userId: user.id,
        timestamp: new Date()
      });

      return true;
    },

    async requestPasswordReset(email) {
      const user = await userRepository.findByEmail(email);
      if (!user) return true;

      const resetToken = uuidv4();
      const expires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

      await userRepository.setPasswordResetToken(user.id, resetToken, expires);

      await publishToRabbit('events', 'notification.send-email', {
        type: 'password-reset',
        to: user.email,
        data: {
          name: user.firstName,
          resetLink: `${process.env.APP_URL}/reset-password?token=${resetToken}`
        }
      });

      return true;
    }
  };
};

module.exports = { createAuthService };
