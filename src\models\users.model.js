const { DataTypes } = require('sequelize');
const bcrypt = require('bcrypt');

const defineUserModel = (sequelize) => {
  const User = sequelize.define('User', {
    UserID: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    FullName: {
      type: DataTypes.STRING(100),
      allowNull: false
    },
    Email: {
      type: DataTypes.STRING(150),
      allowNull: false,
      unique: true,
      validate: { isEmail: true }
    },
    PasswordHash: {
      type: DataTypes.STRING(255),
      allowNull: false
    },
    Phone: {
      type: DataTypes.STRING(20),
      allowNull: false,
      unique: true
    },
    CNIC: {
      type: DataTypes.STRING(15),
      allowNull: false,
      unique: true
    },
    ProfileImageURL: {
      type: DataTypes.STRING,
      allowNull: true
    },
    CreatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    },
    UpdatedAt: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW
    }
  }, {
    tableName: 'users',
    timestamps: false, // manually handling CreatedAt/UpdatedAt
    hooks: {
      beforeCreate: async (user) => {
        if (user.PasswordHash) {
          user.PasswordHash = await bcrypt.hash(user.PasswordHash, 10);
        }
      },
      beforeUpdate: async (user) => {
        if (user.changed('PasswordHash')) {
          user.PasswordHash = await bcrypt.hash(user.PasswordHash, 10);
        }
        user.UpdatedAt = new Date(); // update timestamp
      }
    }
  });

  // Compare password method
  User.prototype.comparePassword = async function (plainPassword) {
    return bcrypt.compare(plainPassword, this.PasswordHash);
  };

  // Clean output
  User.prototype.toJSON = function () {
    const values = { ...this.get() };
    delete values.PasswordHash;
    return values;
  };
  
  User.associate = (models) => {
    // Listing associations
    if (models.Listing) {
      User.hasMany(models.Listing, {
        foreignKey: 'ListerID',
        as: 'Listings',
        onDelete: 'CASCADE'
      });
    }

    // Future associations (commented out until models are created)
    /*
    User.hasMany(models.Bookings, {
      foreignKey: 'ListerID',
      as: 'BookingsAsLister'
    });

    User.hasMany(models.Bookings, {
      foreignKey: 'RenterID',
      as: 'BookingsAsRenter'
    });

    User.hasMany(models.Bookings, {
      foreignKey: 'ConsumerID',
      as: 'BookingsAsConsumer'
    });

    User.hasMany(models.Bookings, {
      foreignKey: 'BuyerID',
      as: 'BookingsAsBuyer'
    });

    User.hasMany(models.Review, {
      foreignKey: 'ReviewerID',
      as: 'ReviewsGiven'
    });

    User.hasMany(models.Review, {
      foreignKey: 'ListerID',
      as: 'ReviewsReceived'
    });

    User.hasMany(models.Chat, {
      foreignKey: 'SenderID',
      as: 'ChatsStarted'
    });

    User.hasMany(models.Chat, {
      foreignKey: 'ReceiverID',
      as: 'ChatsReceived'
    });

    User.hasMany(models.Message, {
      foreignKey: 'SenderID',
      as: 'Messages'
    });

    User.hasMany(models.Notification, {
      foreignKey: 'UserID',
      as: 'Notifications'
    });

    User.hasMany(models.OrderHistory, {
      foreignKey: 'RenterID',
      as: 'OrderHistoriesAsRenter'
    });

    User.hasMany(models.OrderHistory, {
      foreignKey: 'ConsumerID',
      as: 'OrderHistoriesAsConsumer'
    });

    User.hasMany(models.OrderHistory, {
      foreignKey: 'BuyerID',
      as: 'OrderHistoriesAsBuyer'
    });

    User.hasMany(models.OrderHistory, {
      foreignKey: 'ListerID',
      as: 'OrderHistoriesAsLister'
    });

    User.hasMany(models.ListingHistory, {
      foreignKey: 'ListerID',
      as: 'ListingHistoriesAsLister'
    });

    User.hasMany(models.ListingHistory, {
      foreignKey: 'RenterID',
      as: 'ListingHistoriesAsRenter'
    });

    User.hasMany(models.ListingHistory, {
      foreignKey: 'ConsumerID',
      as: 'ListingHistoriesAsConsumer'
    });

    User.hasMany(models.ListingHistory, {
      foreignKey: 'BuyerID',
      as: 'ListingHistoriesAsBuyer'
    });
    */
  };

  return User;
};

module.exports = defineUserModel;
